source "https://rubygems.org"

ruby "3.4.4"
gem "bundler", "~> 2.7.0"
gem "rails", "8.0.2"

gem "aasm", "~> 5.5"
gem "cancancan", "3.6.1"
gem "bootsnap", "1.18.6", require: false
gem "devise", "4.9.4"

gem "httparty", "0.23.1"
gem "jbuilder", "2.13.0"
gem "json_schemer", "2.4.0"
gem "kamal", "2.7.0", require: false
gem "minitest-rails", "8.0.0"
gem "pg", "1.5.9"
gem "propshaft", "1.2.0"
gem "puma", "6.6.0"
gem "sentry-rails", "5.26.0"
gem "solid_cable", "3.0.11"
gem "solid_cache", "1.0.7"
gem "solid_queue", "1.2.0"
gem "thruster", "0.1.14", require: false

# Hotwire stack (Rails 8 includes turbo-rails and stimulus-rails by default)
gem "turbo-rails", "~> 2.0"
gem "stimulus-rails", "~> 1.3"
gem "importmap-rails", "~> 2.1"  # For JavaScript without bundling

group :development, :test do
  gem "brakeman", "7.1.0", require: false
  gem "debug", "1.11.0", platforms: %i[mri mingw mswin x64_mingw], require: "debug/prelude"
  gem "pry-nav", "1.0.0"
  gem "pry-rails", "0.3.11"
  gem "rubocop", "1.78.0", require: false
  gem "rubocop-performance", "1.25.0", require: false
  gem "rubocop-rails", "2.32.0", require: false
  gem "rubocop-rails-omakase", "1.1.0", require: false

  # === CUSTOM GEMS FOR DYNAMIC BUNDLE - CODE QUALITY ===
  gem "reek", "6.5.0", require: false                    # Code smell detection
  gem "rails_best_practices", "1.23.2", require: false   # Rails best practices checker

  # === CUSTOM GEMS FOR DYNAMIC BUNDLE - TESTING ===
  gem "rspec-rails", "8.0.2"                             # RSpec testing framework
  gem "factory_bot_rails", "6.5.0"                       # Test data factories
  gem "faker", "3.5.2"                                   # Fake data generation
  gem "shoulda-matchers", "6.5.0"                        # RSpec matchers for Rails
  gem "simplecov", "0.22.0", require: false              # Code coverage analysis
  # === END CUSTOM GEMS ===
end

group :development do
  gem "web-console", "4.2.1"
end

group :test do
  gem "capybara", "3.40.0"
  gem "selenium-webdriver", "4.34.0"
end
