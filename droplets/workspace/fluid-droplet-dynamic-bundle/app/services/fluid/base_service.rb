# frozen_string_literal: true

require 'httparty'

module Fluid
  # Base service class for all Fluid API integrations
  # Provides common functionality for authentication, HTTP client setup, and error handling
  # Following <PERSON>lar<PERSON>'s HTTParty pattern
  #
  # @example Subclass implementation
  #   class ProductsService < BaseService
  #     def search(query)
  #       with_error_handling do
  #         response = http_client.get("/api/company/v1/products", params: { q: query })
  #         success(response.body)
  #       end
  #     end
  #   end
  #
  class BaseService < ApplicationService
    include HTTParty

    # Fluid API configuration - using same variables as other droplets
    API_VERSION = "v1"

    def initialize(*args)
      super(*args)
      configure_httparty
    end

    private

    def configure_httparty
      self.class.base_uri(api_base_url)

      headers = {
        "Content-Type" => "application/json",
        "Accept" => "application/json",
        "User-Agent" => "DynamicBundleDroplet/1.0"
      }
      headers["Authorization"] = "Bearer #{api_token}" if api_token

      self.class.headers(headers)
    end

    # Get configured HTTP client for Fluid API
    # @return [HTTParty] configured HTTP client using HTTParty
    def http_client
      @http_client ||= HTTParty
    end

    # Authentication configuration for Fluid API
    # @return [Hash] authentication configuration
    def authentication_config
      {
        type: :bearer,
        token: api_token
      }
    end

    # Get API base URL using same variables as other droplets
    # @return [String] the API base URL
    def api_base_url
      # Try FLUID_API_URL first (used by Exigo droplet)
      url = ENV["FLUID_API_URL"]

      # Fallback to constructed URL from subdomain (Avalara pattern)
      if url.blank? && ENV["FLUID_COMPANY_SUBDOMAIN"].present?
        url = "https://#{ENV['FLUID_COMPANY_SUBDOMAIN']}.fluid.app"
      end

      # Final fallback to default
      url.presence || "https://api.fluid.com"
    end

    # Get API token from environment, company context, or configuration
    # @return [String] the API token
    # @raise [StandardError] if token is not configured
    def api_token
      # Priority order:
      # 1. Environment variable (for development/testing)
      # 2. Company authentication token (for production in store)
      # 3. Settings configuration (admin configured)
      # 4. Rails credentials (fallback)
      token = ENV["FLUID_API_TOKEN"] ||
              (defined?(@company) && @company&.authentication_token) ||
              (Setting.respond_to?(:fluid_api) && Setting.fluid_api&.api_key) ||
              Rails.application.credentials.dig(:fluid, :api_token)

      if token.blank?
        raise StandardError, "Fluid API token not configured. Set FLUID_API_TOKEN environment variable, provide company context, configure in admin settings, or configure in credentials."
      end

      token
    end

    # Default headers for all Fluid API requests
    # @return [Hash] default headers
    def default_headers
      {
        "Content-Type" => "application/json",
        "Accept" => "application/json",
        "User-Agent" => "DynamicBundleDroplet/1.0"
      }
    end

    # Handle HTTP response and convert to ServiceResult
    # @param response [Fluid::Http::Client::Response] the HTTP response
    # @return [ServiceResult] success or failure result
    def handle_response(response)
      if response.success?
        Rails.logger.info("Fluid API Success: #{response.status}")
        success(response.body)
      else
        error_message = "Fluid API Error: HTTP #{response.status} - #{response.body}"
        Rails.logger.error(error_message)
        failure(error_message)
      end
    rescue => e
      Rails.logger.error("Fluid API Error: #{e.message}")
      failure("API request failed: #{e.message}")
    end

    # Make GET request to Fluid API
    # @param path [String] the API endpoint path
    # @param params [Hash] query parameters
    # @return [ServiceResult] the API response result
    def get(path, params: {})
      with_error_handling do
        url = "#{api_base_url}#{path}"
        options = {
          query: params,
          headers: {
            'Authorization' => "Bearer #{api_token}",
            'Content-Type' => 'application/json',
            'Accept' => 'application/json'
          },
          timeout: 30
        }

        Rails.logger.info("Making GET request to: #{url}")
        response = HTTParty.get(url, options)
        handle_httparty_response(response)
      end
    end

    # Make POST request to Fluid API
    # @param path [String] the API endpoint path
    # @param body [Hash] request body
    # @return [ServiceResult] the API response result
    def post(path, body: {})
      with_error_handling do
        url = "#{api_base_url}#{path}"
        options = {
          body: body.to_json,
          headers: {
            'Authorization' => "Bearer #{api_token}",
            'Content-Type' => 'application/json',
            'Accept' => 'application/json'
          },
          timeout: 30
        }

        url = "#{api_base_url}#{path}"
        Rails.logger.info("Making POST request to: #{url}")
        response = HTTParty.post(url, options)
        handle_httparty_response(response)
      end
    end

    # Make PUT request to Fluid API
    # @param path [String] the API endpoint path
    # @param body [Hash] request body
    # @return [ServiceResult] the API response result
    def put(path, body: {})
      with_error_handling do
        response = http_client.put(path, body: body)
        handle_response(response)
      end
    end

    # Make PATCH request to Fluid API
    # @param path [String] the API endpoint path
    # @param body [Hash] request body
    # @return [ServiceResult] the API response result
    def patch(path, body: {})
      with_error_handling do
        response = http_client.patch(path, body: body)
        handle_response(response)
      end
    end

    # Make DELETE request to Fluid API
    # @param path [String] the API endpoint path
    # @return [ServiceResult] the API response result
    def delete(path)
      with_error_handling do
        response = http_client.delete(path)
        handle_response(response)
      end
    end

    # Handle HTTParty response
    # @param response [HTTParty::Response] the HTTParty response
    # @return [ServiceResult] the processed response
    def handle_httparty_response(response)
      if response.success?
        ServiceResult.success(data: response.parsed_response)
      else
        error_message = "HTTP #{response.code}: #{response.message}"
        if response.parsed_response.is_a?(Hash) && response.parsed_response['error']
          error_message += " - #{response.parsed_response['error']}"
        end
        ServiceResult.failure(error: error_message)
      end
    end
  end
end
