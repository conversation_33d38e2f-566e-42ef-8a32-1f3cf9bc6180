# frozen_string_literal: true

module Admin
  # Bundle Builder Controller - Full-page drag & drop interface
  # Part of the Hybrid Approach for bundle creation
  class BundleBuilderController < ApplicationController
    layout 'bundle_admin'

    before_action :authenticate_admin!
    before_action :ensure_company_context
    before_action :load_bundle_draft, only: [:new, :create]
    before_action :set_bundle, only: [:edit, :update]

    # GET /admin/bundles/new/builder
    # Full-page builder interface for new bundles
    def new
      unless @bundle_draft
        flash[:error] = "No bundle draft found. Please start by creating a bundle."
        redirect_to admin_bundles_path
        return
      end

      @bundle = @bundle_draft
      @categories = []
      @available_products = fetch_available_products
      
      Rails.logger.info("BUILDER: Loading new bundle builder for #{@bundle['name']}")
    end

    # POST /admin/bundles/builder
    # Save the complete bundle configuration
    def create
      unless @bundle_draft
        flash[:error] = "No bundle draft found. Please start by creating a bundle."
        redirect_to admin_bundles_path
        return
      end

      Rails.logger.info("BUILDER: Saving bundle configuration: #{builder_params.inspect}")

      # Process categories from JSON data
      categories_json = params[:categories]
      if categories_json.present?
        begin
          categories_data = JSON.parse(categories_json)
          Rails.logger.info("BUILDER: Parsed categories data: #{categories_data.inspect}")
        rescue JSON::ParserError => e
          Rails.logger.error("BUILDER: Failed to parse categories JSON: #{e.message}")
          flash[:error] = "Invalid categories data format"
          redirect_to new_bundle_builder_admin_bundles_path
          return
        end
      else
        categories_data = []
      end

      # Update bundle with categories and save to Fluid API
      @bundle_draft['categories'] = categories_data
      @bundle_draft['status'] = 'active' # Activate when saved from builder
      @bundle_draft['updated_at'] = Time.current.iso8601

      # Save bundle with categories to Fluid API
      result = Fluid::BundlesService.call(
        action: :update,
        bundle_id: @bundle_draft['id'],
        name: @bundle_draft['name'],
        description: @bundle_draft['description'],
        metadata: {
          categories: categories_data,
          status: 'active'
        },
        company: @company
      )

      if result.success?
        # Clear the draft from session
        session.delete(:bundle_draft)

        flash[:success] = "Bundle '#{@bundle_draft['name']}' created successfully with #{categories_data.length} categories!"
        redirect_to admin_bundles_path
        Rails.logger.info("BUILDER: Bundle saved successfully to Fluid API with ID #{@bundle_draft['id']}")
      else
        Rails.logger.error("BUILDER: Failed to save bundle to Fluid API: #{result.error}")
        flash.now[:error] = "Failed to save bundle: #{result.error}"
        @categories = categories_data
        @available_products = fetch_available_products
        render :new, status: :unprocessable_entity
      end
    end

    # GET /admin/bundles/:id/edit/builder
    # Full-page builder interface for existing bundles
    def edit
      @categories = @bundle.dig('metadata', 'categories') || []
      @available_products = fetch_available_products
      
      Rails.logger.info("BUILDER: Loading edit builder for bundle #{@bundle['id']}")
    end

    # PATCH /admin/bundles/:id/builder
    # Update existing bundle configuration
    def update
      Rails.logger.info("BUILDER: Updating bundle #{@bundle['id']}: #{builder_params.inspect}")

      # Process categories and products from the builder
      categories_data = process_categories_data(builder_params[:categories] || [])

      # Update bundle via Fluid API
      result = Fluid::BundlesService.call(
        action: :update,
        bundle_id: @bundle['id'],
        name: @bundle['name'],
        description: @bundle['description'],
        metadata: {
          categories: categories_data,
          status: @bundle['status'] || 'active'
        },
        company: @company
      )

      if result.success?
        flash[:success] = "Bundle '#{@bundle['name']}' updated successfully!"
        redirect_to admin_bundles_path
        Rails.logger.info("BUILDER: Bundle updated successfully via Fluid API")
      else
        Rails.logger.error("BUILDER: Failed to update bundle via Fluid API: #{result.error}")
        flash.now[:error] = "Failed to update bundle: #{result.error}"
        @categories = categories_data
        @available_products = fetch_available_products
        render :edit, status: :unprocessable_entity
      end
    end

    private

    # Load bundle draft from session (for new bundles)
    def load_bundle_draft
      @bundle_draft = session[:bundle_draft]
    end

    # Set bundle for editing (load from Fluid API)
    def set_bundle
      bundle_id = params[:id] || params[:bundle_id]

      Rails.logger.info("BUILDER: Loading bundle #{bundle_id} from Fluid API")

      result = Fluid::BundlesService.call(
        action: :find,
        bundle_id: bundle_id,
        company: @company
      )

      if result.success?
        @bundle = result.data[:bundle]
        Rails.logger.info("BUILDER: Successfully loaded bundle #{bundle_id}")
      else
        Rails.logger.error("BUILDER: Failed to load bundle #{bundle_id}: #{result.error}")
        flash[:error] = "Bundle not found: #{result.error}"
        redirect_to admin_bundles_path
      end
    end

    # Fetch available products from Fluid API
    def fetch_available_products
      Rails.logger.info("BUILDER: Fetching available products from Fluid API")

      result = Fluid::ProductsService.call(
        action: :list,
        page: 1,
        per_page: 100, # Get more products for bundle building
        company: @company
      )

      if result.success?
        products = result.data[:products] || []
        Rails.logger.info("BUILDER: Successfully fetched #{products.size} products")
        return products
      else
        Rails.logger.error("BUILDER: Failed to fetch products: #{result.error}")
        # Return empty array if API fails
        return []
      end
    rescue => e
      Rails.logger.error("BUILDER: Error fetching products: #{e.message}")
      return []
    end

    # Process categories data from form
    def process_categories_data(categories_params)
      return [] unless categories_params.is_a?(Array)

      categories_params.map.with_index do |category_data, index|
        {
          "id" => category_data[:id] || SecureRandom.uuid,
          "name" => category_data[:name] || "Category #{index + 1}",
          "position" => index + 1,
          "required" => category_data[:required] == "1",
          "max_selections" => category_data[:max_selections]&.to_i || 1,
          "products" => process_products_data(category_data[:products] || [])
        }
      end
    end

    # Process products data for a category
    def process_products_data(products_params)
      return [] unless products_params.is_a?(Array)

      products_params.map.with_index do |product_data, index|
        {
          "id" => product_data[:id],
          "name" => product_data[:name],
          "sku" => product_data[:sku],
          "price" => product_data[:price]&.to_f,
          "position" => index + 1,
          "default" => product_data[:default] == "1"
        }
      end
    end

    # Strong parameters for builder
    def builder_params
      params.permit(
        :bundle_id,
        :categories,
        categories: [
          :id, :name, :required, :max_selections,
          products: [:id, :name, :sku, :price, :default]
        ]
      )
    end

    # Ensure company context is available
    def ensure_company_context
      unless @company
        if Rails.env.development?
          @company = OpenStruct.new(
            fluid_company_id: *********,
            name: "Development Company",
            droplet_installation_uuid: "dev-dri-123",
            authentication_token: "your_real_fluid_token_here"
          )
          Rails.logger.info("HARDCODED: Using development company (ID: #{@company.fluid_company_id})")
          return
        end

        handle_missing_company_context
      end
    end

    # Admin authentication for bundle builder
    def authenticate_admin!
      if Rails.env.development?
        Rails.logger.info("DEVELOPMENT: Admin access granted for bundle builder")
        return true
      end

      unless @company&.authentication_token.present?
        Rails.logger.warn("Bundle builder access denied: No valid company context")
        redirect_to root_path, alert: "Access denied: Invalid company context"
        return false
      end

      Rails.logger.info("Bundle builder access granted for company: #{@company.name}")
      true
    end
  end
end
