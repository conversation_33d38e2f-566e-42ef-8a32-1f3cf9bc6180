<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= content_for?(:title) ? yield(:title) : "Dynamic Bundle Admin" %></title>
  
  <link rel="icon" href="/icon.png" type="image/png">
  <link rel="icon" href="/icon.svg" type="image/svg+xml">
  <link rel="apple-touch-icon" href="/icon.png">
  
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>
  
  <!-- Simple approach: Just load what we need -->
  <script src="https://unpkg.com/@hotwired/turbo@8.0.12/dist/turbo.es2017-umd.js"></script>
  <script src="https://unpkg.com/@hotwired/stimulus@3.2.2/dist/stimulus.umd.js"></script>

  <script>
    // Simple Stimulus setup
    const application = Stimulus.Application.start()

    // Bundle Wizard Controller (inline for now)
    class BundleWizardController extends Stimulus.Controller {
      static targets = ["step", "form", "nextButton", "prevButton", "progressBar"]
      static values = {
        currentStep: String,
        totalSteps: Number,
        wizardState: String
      }

      connect() {
        console.log("🧙‍♂️ Bundle Wizard Controller connected")
        console.log("Current step:", this.currentStepValue)
      }

      nextStep(event) {
        const currentStep = this.currentStepValue
        console.log("🚀 Next step clicked, current step:", currentStep)

        // For preview step (Complete Bundle), let the form submit naturally
        if (currentStep === 'preview') {
          console.log("🎯 Preview step - letting form submit naturally")
          // Don't prevent default, let the form submit
          return true
        }

        // For other steps, handle custom logic
        event.preventDefault()

        if (currentStep === 'builder') {
          // For builder step, collect products and navigate to preview
          this.handleBuilderNext()
        } else if (this.hasFormTarget) {
          // For other steps, submit the form normally
          this.formTarget.submit()
        }
      }

      handleBuilderNext() {
        console.log("🏗️ Handling builder next step")

        // Collect selected products
        const selectedProducts = []
        const productCards = document.querySelectorAll('.bundle-product-card')

        productCards.forEach(card => {
          const productId = card.dataset.productId
          const productName = card.querySelector('.bundle-product-name')?.textContent
          const productPrice = card.querySelector('.bundle-product-price')?.textContent

          if (productId && productName) {
            selectedProducts.push({
              id: productId,
              name: productName,
              price: productPrice
            })
          }
        })

        console.log("📦 Selected products:", selectedProducts)

        // Create form data
        const formData = new FormData()
        formData.append('authenticity_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'))
        formData.append('builder_config', JSON.stringify({
          products: selectedProducts,
          total_products: selectedProducts.length
        }))
        formData.append('direction', 'next')

        // Submit to builder endpoint
        fetch('/admin/bundles/wizard/step/builder', {
          method: 'POST',
          body: formData,
          headers: {
            'Accept': 'text/vnd.turbo-stream.html, text/html, application/xhtml+xml'
          }
        })
        .then(response => {
          if (response.ok) {
            // Navigate to preview
            window.location.href = '/admin/bundles/wizard/step/preview'
          } else {
            console.error('❌ Failed to save builder data')
            alert('Failed to save products. Please try again.')
          }
        })
        .catch(error => {
          console.error('❌ Error:', error)
          alert('Error saving products. Please try again.')
        })
      }

      prevStep(event) {
        event.preventDefault()
        console.log("⬅️ Previous step clicked")

        const currentStep = this.currentStepValue
        console.log("Current step:", currentStep)

        // Navigate to previous step
        if (currentStep === 'builder') {
          window.location.href = '/admin/bundles/wizard/step/info'
        } else if (currentStep === 'preview') {
          window.location.href = '/admin/bundles/wizard/step/builder'
        } else {
          window.history.back()
        }
      }
    }

    // Bundle Builder Controller (inline for now)
    class BundleBuilderController extends Stimulus.Controller {
      static targets = ["categoryGrid", "productGrid", "dropZone", "selectedProducts"]

      connect() {
        console.log("🏗️ Bundle Builder Controller connected")
        this.initializeDragAndDrop()
      }

      selectCategory(event) {
        const categoryButton = event.currentTarget
        const categoryId = categoryButton.dataset.categoryId
        const categoryName = categoryButton.dataset.categoryName

        console.log(`📂 Category selected: ${categoryName}`)

        // Update UI
        this.categoryGridTarget.querySelectorAll('.category-card').forEach(card => {
          card.classList.remove('active', 'border-primary')
        })
        categoryButton.classList.add('active', 'border-primary')

        // Load products
        this.loadCategoryProducts(categoryId, categoryButton)
      }

      loadCategoryProducts(categoryId, categoryElement) {
        try {
          const products = JSON.parse(categoryElement.dataset.products || '[]')
          console.log('📦 Products found:', products)
          this.renderProducts(products)
        } catch (error) {
          console.error('❌ Error parsing products:', error)
        }
      }

      renderProducts(products) {
        if (products && products.length > 0) {
          this.productGridTarget.innerHTML = products.map(product => `
            <div class="product-card" draggable="true"
                 data-product-id="${product.id}"
                 data-product-name="${product.title || product.name}"
                 data-product-price="${product.price_in_currency || product.price}">
              <div class="product-image">
                ${product.image_url ?
                  `<img src="${product.image_url}" alt="${product.title}" style="width: 100%; height: 60px; object-fit: cover; border-radius: 4px;">` :
                  '<div style="width: 100%; height: 60px; background: #f8f9fa; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 24px;">📦</div>'
                }
              </div>
              <div class="product-name">${product.title || product.name}</div>
              <div class="product-price">${product.price_in_currency || product.price}</div>
            </div>
          `).join('')

          this.setupDraggableProducts()
        }
      }

      initializeDragAndDrop() {
        this.setupDropZone()
        // Load first category
        const firstCategory = this.categoryGridTarget.querySelector('.category-card.active')
        if (firstCategory) {
          this.loadCategoryProducts(firstCategory.dataset.categoryId, firstCategory)
        }
      }

      setupDraggableProducts() {
        const productCards = this.productGridTarget.querySelectorAll('.product-card')
        productCards.forEach(card => {
          card.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', JSON.stringify({
              id: card.dataset.productId,
              name: card.dataset.productName,
              price: card.dataset.productPrice
            }))
            card.classList.add('dragging')
          })

          card.addEventListener('dragend', () => {
            card.classList.remove('dragging')
          })
        })
      }

      setupDropZone() {
        if (!this.hasDropZoneTarget) return

        const dropZone = this.dropZoneTarget

        dropZone.addEventListener('dragover', (e) => {
          e.preventDefault()
          dropZone.classList.add('drag-over')
        })

        dropZone.addEventListener('dragleave', (e) => {
          if (!dropZone.contains(e.relatedTarget)) {
            dropZone.classList.remove('drag-over')
          }
        })

        dropZone.addEventListener('drop', (e) => {
          e.preventDefault()
          dropZone.classList.remove('drag-over')

          try {
            const productData = JSON.parse(e.dataTransfer.getData('text/plain'))
            console.log('🎯 Product dropped:', productData.name)
            this.addProductToBundle(productData)
          } catch (error) {
            console.error('❌ Error dropping product:', error)
          }
        })
      }

      addProductToBundle(product) {
        const emptyState = this.dropZoneTarget.querySelector('.drop-zone-empty')
        if (emptyState) {
          emptyState.style.display = 'none'
        }

        const productHtml = `
          <div class="bundle-product-card" data-product-id="${product.id}">
            <div class="bundle-product-info">
              <div class="bundle-product-name">${product.name}</div>
              <div class="bundle-product-price">${product.price}</div>
            </div>
            <button class="btn btn-sm btn-outline-danger" onclick="this.parentElement.remove()">×</button>
          </div>
        `

        this.selectedProductsTarget.insertAdjacentHTML('beforeend', productHtml)
        console.log('✅ Product added to bundle:', product.name)
      }
    }

    // Register controllers
    application.register("bundle-wizard", BundleWizardController)
    application.register("bundle-builder", BundleBuilderController)

    console.log("🎮 Stimulus loaded with inline controllers!")
  </script>

  <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #f5f5f7;
    }

    .admin-header {
      background: #2c3e50;
      color: white;
      padding: 10px 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .admin-header h1 {
      font-size: 20px;
      font-weight: 500;
      margin: 0;
    }

    .header-actions {
      display: flex;
      gap: 10px;
      align-items: center;
    }

    .header-actions .btn {
      padding: 6px 12px;
      font-size: 14px;
      border-radius: 6px;
      border: 1px solid transparent;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .header-actions .btn-success {
      background: #28a745;
      color: white;
      border-color: #28a745;
    }

    .header-actions .btn-success:hover {
      background: #218838;
      border-color: #1e7e34;
    }

    .header-actions .btn-outline-secondary {
      background: transparent;
      color: #6c757d;
      border-color: #6c757d;
    }

    .header-actions .btn-outline-secondary:hover {
      background: #6c757d;
      color: white;
    }

    .header-actions .btn-link {
      background: none;
      border: none;
      color: #adb5bd;
      font-size: 18px;
      padding: 4px 8px;
    }

    .header-actions .btn-link:hover {
      color: white;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 15px;
    }

    /* Tab Navigation */
    .tabs {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      overflow: hidden;
    }

    .tab-nav {
      display: flex;
      border-bottom: 1px solid #e1e5e9;
    }

    .tab-nav a,
    .tab-nav button,
    .tab-nav span {
      background: none;
      border: none;
      padding: 15px 25px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      color: #666;
      border-bottom: 3px solid transparent;
      transition: all 0.2s ease;
      text-decoration: none;
      display: inline-block;
    }

    .tab-nav a:hover,
    .tab-nav button:hover {
      background-color: #f8f9fa;
      color: #333;
    }

    .tab-nav a.active,
    .tab-nav button.active {
      color: #007bff;
      border-bottom-color: #007bff;
      background-color: #f8f9ff;
    }

    .tab-nav .disabled {
      color: #adb5bd;
      cursor: not-allowed;
      opacity: 0.6;
    }

    .tab-nav .disabled:hover {
      background-color: transparent;
      color: #adb5bd;
    }

    /* Flash Messages */
    .flash-messages {
      margin-bottom: 20px;
    }

    .flash-message {
      padding: 12px 16px;
      border-radius: 6px;
      margin-bottom: 10px;
      font-weight: 500;
    }

    .flash-message.success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .flash-message.error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .flash-message.notice {
      background-color: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }

    /* Content Area */
    .content-area {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      padding: 24px;
      min-height: 500px;
    }

    /* Buttons */
    .btn {
      display: inline-block;
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      text-decoration: none;
      cursor: pointer;
      transition: all 0.2s ease;
      text-align: center;
    }

    .btn-primary {
      background-color: #007bff;
      color: white;
    }

    .btn-primary:hover {
      background-color: #0056b3;
      color: white;
    }

    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background-color: #545b62;
      color: white;
    }

    .btn-success {
      background-color: #28a745;
      color: white;
    }

    .btn-success:hover {
      background-color: #1e7e34;
      color: white;
    }

    .btn-danger {
      background-color: #dc3545;
      color: white;
    }

    .btn-danger:hover {
      background-color: #c82333;
      color: white;
    }

    .btn-sm {
      padding: 6px 12px;
      font-size: 12px;
    }

    /* Tables */
    .table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .table th,
    .table td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #e1e5e9;
    }

    .table th {
      background-color: #f8f9fa;
      font-weight: 600;
      color: #495057;
    }

    .table tbody tr:hover {
      background-color: #f8f9fa;
    }

    /* Forms */
    .form-group {
      margin-bottom: 20px;
    }

    .form-label {
      display: block;
      margin-bottom: 6px;
      font-weight: 500;
      color: #495057;
    }

    .form-control {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #ced4da;
      border-radius: 6px;
      font-size: 14px;
      transition: border-color 0.2s ease;
    }

    .form-control:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    .form-text {
      font-size: 12px;
      color: #6c757d;
      margin-top: 4px;
    }

    /* Utilities */
    .text-center { text-align: center; }
    .text-right { text-align: right; }
    .mb-3 { margin-bottom: 1rem; }
    .mt-3 { margin-top: 1rem; }
    .d-flex { display: flex; }
    .justify-content-between { justify-content: space-between; }
    .align-items-center { align-items: center; }
  </style>
</head>

<body>
  <header class="admin-header">
    <div class="container d-flex justify-content-between align-items-center">
      <h1 id="contextual-title">Dynamic Bundle Admin</h1>
      <div class="header-actions" id="header-actions">
        <!-- Actions will be inserted here dynamically -->
      </div>
    </div>
  </header>

  <div class="container">
    <!-- Tab Navigation - HIDDEN -->
    <div class="tabs" style="display: none;">
      <div class="tab-nav">
        <%= link_to "Bundles", admin_bundles_path,
            class: "#{'active' if controller_name == 'bundles'}" %>
        <% if controller_name == 'categories' && params[:bundle_id] %>
          <%= link_to "Categories", admin_bundle_categories_path(params[:bundle_id]),
              class: "active" %>
        <% else %>
          <span class="disabled">Categories</span>
        <% end %>
        <button>Products</button>
        <button>Settings</button>
      </div>
    </div>

    <!-- Flash Messages - HIDDEN -->
    <% if flash.any? %>
      <div class="flash-messages" style="display: none;">
        <% flash.each do |type, message| %>
          <div class="flash-message <%= type %>">
            <%= message %>
          </div>
        <% end %>
      </div>
    <% end %>

    <!-- Main Content -->
    <div class="content-area">
      <%= yield %>
    </div>
  </div>

  <!-- Modals and overlays go here -->
  <%= content_for :modals %>
</body>
</html>
