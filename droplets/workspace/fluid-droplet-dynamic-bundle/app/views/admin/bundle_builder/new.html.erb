<% content_for :title, "Bundle Builder - #{@bundle['name']}" %>

<div class="bundle-builder-container">
  <!-- Minimal Header -->
  <div class="builder-header-minimal">
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <h3 class="mb-0">
          <span class="me-2">🏗️</span>Bundle Builder
          <small class="text-muted ms-2"><%= @bundle['name'] %></small>
        </h3>
      </div>
      <div>
        <%= link_to "← Back", admin_bundles_path, class: "btn btn-outline-secondary btn-sm" %>
      </div>
    </div>
  </div>

  <!-- Full Screen Builder Interface -->
  <%= form_with url: bundle_builder_admin_bundles_path, method: :post, local: true, id: "bundleBuilderForm" do |form| %>
    <div class="builder-main-container">
      <!-- Left Half - Store Catalog (divided in 2) -->
      <div class="builder-left-half">
        <!-- Top: Categories -->
        <div class="categories-section">
          <div class="section-header">
            <h5><span class="me-2">🏪</span>Store Categories</h5>
          </div>
          <div class="store-categories-container" id="storeCategoriesContainer">
            <div class="categories-grid">
              <div class="store-category-card active" data-category="supplements">
                <div class="category-icon">💊</div>
                <div class="category-name">Supplements</div>
              </div>
              <div class="store-category-card" data-category="nutrition">
                <div class="category-icon">🥗</div>
                <div class="category-name">Nutrition</div>
              </div>
              <div class="store-category-card" data-category="fitness">
                <div class="category-icon">💪</div>
                <div class="category-name">Fitness</div>
              </div>
              <div class="store-category-card" data-category="wellness">
                <div class="category-icon">🧘</div>
                <div class="category-name">Wellness</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Bottom: Products -->
        <div class="products-section">
          <div class="section-header">
            <h5><span class="me-2">📦</span>Products <small class="text-muted" id="selectedCategoryName">Supplements</small></h5>
          </div>
          <div class="store-products-container" id="storeProductsContainer">
            <div class="products-grid" id="storeProductsGrid">
              <!-- Products will be loaded dynamically -->
            </div>
          </div>
        </div>
      </div>

      <!-- Right Half - Bundle Drop Zone (full height) -->
      <div class="builder-right-half">
        <div class="section-header">
          <h5><span class="me-2">📦</span>Bundle Products <small class="text-muted">(Drop products here)</small></h5>
        </div>

        <div class="bundle-drop-zone-full" id="bundleDropZone" ondrop="handleDrop(event)" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
          <!-- Empty state -->
          <div class="drop-zone-empty" id="dropZoneEmpty" style="pointer-events: none;">
            <div class="drop-zone-icon">📦</div>
            <div class="drop-zone-text">Drop products here to add to bundle</div>
            <small class="text-muted">Drag products from the left panel</small>
          </div>

          <!-- Selected products will appear here as cards -->
          <div class="selected-products-grid" id="selectedProductsGrid" style="pointer-events: none;">
            <!-- Product cards will be added here -->
          </div>
        </div>

        <!-- Action Button Area -->
        <div class="bundle-actions">
          <div class="d-flex justify-content-between align-items-center">
            <div class="bundle-info">
              <span class="text-muted">Building: <strong><%= @bundle['name'] %></strong></span>
              <span class="badge bg-light text-dark ms-2" id="selectedProductsCount">0 products selected</span>
            </div>
            <button type="button" class="btn btn-primary" id="saveBundleBtn">
              <span class="me-1">💾</span><span id="saveBtnText">Save & Continue →</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  <% end %>
</div>

<!-- Category Template (Hidden) -->
<!-- Bundle Category Template (Hidden) -->
<template id="bundleCategoryTemplate">
  <div class="bundle-category-card" data-category-index="">
    <div class="category-header">
      <div class="category-drag-handle">⋮⋮</div>
      <input type="text" class="category-name-input" placeholder="Category Name" required>
      <div class="category-actions">
        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeBundleCategory(this)">
          🗑️
        </button>
      </div>
    </div>

    <div class="category-settings">
      <div class="row">
        <div class="col-6">
          <label class="form-check">
            <input type="checkbox" class="form-check-input category-required">
            <span class="form-check-label">Required</span>
          </label>
        </div>
        <div class="col-6">
          <label class="form-label">Max Selections:</label>
          <input type="number" class="form-control form-control-sm category-max-selections" value="1" min="1">
        </div>
      </div>
    </div>

    <div class="category-products-drop-zone" data-category-products="">
      <div class="products-drop-zone" id="dropZone">
        <div class="drop-zone-content">
          <span class="drop-zone-icon">📦</span>
          <span class="drop-zone-text">Drop products here</span>
        </div>
      </div>
      <div class="dropped-products" style="display: none;">
        <!-- Dropped products will appear here -->
      </div>
    </div>
  </div>
</template>

<style>
  .bundle-builder-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
  }
  
  .builder-header {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .builder-panel {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .panel-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 15px 20px;
    display: flex;
    justify-content: between;
    align-items: center;
  }
  
  /* Store Categories Styles */
  .store-categories-container {
    padding: 15px;
  }

  .categories-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .store-category-card {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .store-category-card:hover {
    background: #e9ecef;
    border-color: #007bff;
  }

  .store-category-card.active {
    background: #e3f2fd;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }

  .category-icon {
    font-size: 24px;
    margin-bottom: 5px;
  }

  .category-name {
    font-size: 12px;
    font-weight: 600;
    color: #495057;
  }

  /* Store Products Styles */
  .store-products-container {
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
  }

  .products-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .store-product-card {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 10px;
    text-align: center;
    cursor: move;
    transition: all 0.2s ease;
  }

  .store-product-card:hover {
    background: #f8f9fa;
    border-color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .store-product-card.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
  }

  .product-emoji {
    font-size: 20px;
    margin-bottom: 4px;
  }

  .product-name {
    font-size: 11px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 2px;
  }

  .product-price {
    font-size: 10px;
    color: #6c757d;
  }

  /* Bundle Categories Styles */
  .bundle-categories-container {
    padding: 20px;
    min-height: 400px;
  }

  .bundle-category-card {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.2s ease;
  }
  
  .category-card:hover {
    border-color: #667eea;
    background: #f0f4ff;
  }
  
  .category-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
  }
  
  .category-drag-handle {
    cursor: grab;
    color: #6c757d;
    font-size: 18px;
  }
  
  .category-name-input {
    flex: 1;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 8px 12px;
    font-weight: 500;
  }
  
  .category-settings {
    margin-bottom: 15px;
  }
  
  .products-drop-zone {
    border: 2px dashed #ced4da;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .products-drop-zone.drag-over {
    border-color: #28a745;
    background: #d4edda;
    transform: scale(1.02);
  }

  .drop-zone-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }

  .drop-zone-icon {
    font-size: 24px;
    opacity: 0.5;
  }

  .drop-zone-text {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
  }

  /* Dropped Products Styles */
  .dropped-products {
    margin-top: 10px;
  }

  .dropped-product-item {
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .dropped-product-info {
    flex: 1;
  }

  .dropped-product-name {
    font-weight: 600;
    font-size: 13px;
    color: #1976d2;
  }

  .dropped-product-price {
    font-size: 11px;
    color: #666;
  }

  .remove-product-btn {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
  }

  .remove-product-btn:hover {
    background: #f8d7da;
  }

  /* Bundle Drop Zone Styles */
  .bundle-drop-zone {
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    padding: 20px;
    background: #f8f9fa;
    transition: all 0.3s ease;
    min-height: 300px;
    position: relative;
  }

  .bundle-drop-zone.drag-over {
    border-color: #28a745;
    background: #d4edda;
  }

  .drop-zone-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 12px;
    height: 260px;
    text-align: center;
  }

  .drop-zone-icon {
    font-size: 48px;
    opacity: 0.5;
  }

  .drop-zone-text {
    font-size: 18px;
    font-weight: 600;
    color: #6c757d;
  }

  .selected-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    padding: 10px;
  }

  .selected-product-card {
    background: white;
    border: 1px solid #e3f2fd;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    position: relative;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .selected-product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  }

  .selected-product-emoji {
    font-size: 32px;
    margin-bottom: 8px;
  }

  .selected-product-name {
    font-weight: 600;
    color: #1976d2;
    margin-bottom: 4px;
    font-size: 14px;
  }

  .selected-product-price {
    color: #666;
    font-size: 13px;
  }

  .remove-product-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.8;
    transition: opacity 0.2s ease;
  }

  .remove-product-btn:hover {
    opacity: 1;
    transform: scale(1.1);
  }

  /* Selected Products Styles */
  .selected-products-container {
    margin-top: 20px;
    padding: 20px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
  }

  .selected-product-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    margin-bottom: 8px;
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 6px;
  }

  .selected-product-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .selected-product-emoji {
    font-size: 24px;
  }

  .selected-product-details {
    flex: 1;
  }

  .selected-product-name {
    font-weight: 600;
    color: #1976d2;
    margin-bottom: 2px;
  }

  .selected-product-price {
    font-size: 14px;
    color: #666;
  }

  .product-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .product-info {
    flex: 1;
  }
  
  .product-name {
    font-weight: 500;
    color: #495057;
  }
  
  .product-details {
    font-size: 12px;
    color: #6c757d;
    display: flex;
    gap: 10px;
  }
  
  .product-sku {
    font-family: monospace;
    background: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
  }
  
  .drag-handle {
    color: #6c757d;
    cursor: grab;
  }
  
  .bundle-preview {
    padding: 20px;
  }
  
  .preview-header h6 {
    margin-bottom: 4px;
    color: #495057;
  }
  
  .preview-description {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 15px;
    line-height: 1.4;
  }
  
  .stat {
    display: flex;
    justify-content: between;
    margin-bottom: 5px;
  }
  
  .stat-label {
    font-size: 13px;
    color: #6c757d;
  }
  
  .stat-value {
    font-weight: 500;
    color: #495057;
  }

  /* Drag and Drop Enhancements */
  .product-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
  }

  /* Hide flash messages and navigation tabs in builder - AGGRESSIVE */
  .alert,
  .alert-success,
  .alert-info,
  .alert-warning,
  div[class*="alert"] {
    display: none !important;
  }

  /* Hide ALL navigation elements during wizard */
  .nav,
  .nav-tabs,
  .nav-pills,
  ul.nav,
  nav,
  [role="navigation"],
  div:has(> a:contains("Bundles")),
  div:has(> a:contains("Categories")),
  div:has(> a:contains("Products")),
  div:has(> a:contains("Settings")) {
    display: none !important;
  }

  /* Target specific Bootstrap tab structure */
  .container .nav,
  .container ul.nav,
  .container .nav-tabs {
    display: none !important;
  }

  /* New Full Screen Layout */
  .bundle-builder-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .builder-header-minimal {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 15px 20px;
    flex-shrink: 0;
  }

  .builder-main-container {
    display: flex;
    flex: 1;
    overflow: hidden;
  }

  .builder-left-half {
    width: 50%;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #e9ecef;
  }

  .builder-right-half {
    width: 50%;
    display: flex;
    flex-direction: column;
  }

  .categories-section {
    height: 50%;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    overflow-y: auto;
  }

  .products-section {
    height: 50%;
    padding: 20px;
    overflow-y: auto;
  }

  .section-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
  }

  .section-header h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
  }

  .bundle-drop-zone-full {
    flex: 1;
    margin: 20px 20px 0 20px;
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    padding: 20px;
    background: #f8f9fa;
    transition: all 0.3s ease;
    overflow-y: auto;
    position: relative;
  }

  .bundle-drop-zone-full.drag-over {
    border-color: #28a745;
    background: #d4edda;
  }

  .bundle-actions {
    padding: 20px;
    background: white;
    border-top: 1px solid #e9ecef;
    flex-shrink: 0;
  }

  .bundle-info {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .category-product {
    background: #e8f5e8 !important;
    border-color: #28a745 !important;
    position: relative;
  }

  .category-product .btn-outline-danger {
    position: absolute;
    top: 5px;
    right: 5px;
    padding: 2px 6px;
    font-size: 12px;
    line-height: 1;
  }

  .preview-category {
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #667eea;
  }

  .form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
  }

  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  console.log('Bundle Builder loaded for:', '<%= @bundle['name'] %>');

  // Check if this is part of the wizard flow
  const isWizardFlow = window.location.search.includes('bundle_id=') ||
                       document.referrer.includes('/admin/bundles/new') ||
                       sessionStorage.getItem('bundle_wizard_active') === 'true';

  // Set up contextual header for wizard
  if (isWizardFlow && window.updateContextualHeader) {
    const bundleName = '<%= j @bundle['name'] %>';

    window.updateContextualHeader(
      `🧙‍♂️ Bundle Creation Wizard - Step 2/3`,
      [
        {
          text: '← Back to Step 1',
          class: 'btn-outline-secondary',
          onclick: 'backToWizardStep1()'
        },
        {
          text: 'Continue to Review →',
          class: 'btn-primary',
          onclick: 'continueToWizardStep3()'
        }
      ]
    );

    // Mark wizard as active
    sessionStorage.setItem('bundle_wizard_active', 'true');

    // Update save button text and behavior for wizard
    const saveBtnText = document.getElementById('saveBtnText');
    if (saveBtnText) {
      saveBtnText.textContent = 'Continue to Review →';
    }

    // Override save button behavior for wizard
    const saveBtn = document.getElementById('saveBundleBtn');
    if (saveBtn) {
      saveBtn.onclick = continueToWizardStep3;
    }
  }

  let bundleCategoryCounter = 0;
  let draggedProduct = null;
  let bundleData = {
    categories: []
  };

  // Mock store data
  const storeData = {
    supplements: [
      { id: 1, name: 'Vitamin D3', price: 24.99, emoji: '☀️' },
      { id: 2, name: 'Omega-3', price: 32.99, emoji: '🐟' },
      { id: 3, name: 'Probiotics', price: 28.99, emoji: '🦠' },
      { id: 4, name: 'Magnesium', price: 19.99, emoji: '⚡' }
    ],
    nutrition: [
      { id: 5, name: 'Protein Powder', price: 45.99, emoji: '💪' },
      { id: 6, name: 'Green Smoothie', price: 38.99, emoji: '🥬' },
      { id: 7, name: 'Energy Bars', price: 24.99, emoji: '🍫' },
      { id: 8, name: 'Meal Replacement', price: 52.99, emoji: '🥤' }
    ],
    fitness: [
      { id: 9, name: 'Pre-Workout', price: 34.99, emoji: '🔥' },
      { id: 10, name: 'BCAA', price: 29.99, emoji: '💊' },
      { id: 11, name: 'Creatine', price: 22.99, emoji: '⚡' },
      { id: 12, name: 'Recovery Drink', price: 31.99, emoji: '🧊' }
    ],
    wellness: [
      { id: 13, name: 'Meditation App', price: 9.99, emoji: '🧘' },
      { id: 14, name: 'Sleep Support', price: 26.99, emoji: '😴' },
      { id: 15, name: 'Stress Relief', price: 23.99, emoji: '🌿' },
      { id: 16, name: 'Immunity Boost', price: 35.99, emoji: '🛡️' }
    ]
  };

  // Selected products array
  let selectedProducts = [];

  // Global drop handlers
  window.handleDragOver = function(e) {
    console.log('Inline dragover triggered');
    e.preventDefault();
    e.stopPropagation();
    e.target.classList.add('drag-over');
    return false;
  }

  window.handleDragLeave = function(e) {
    console.log('Inline dragleave triggered');
    e.target.classList.remove('drag-over');
  }

  window.handleDrop = function(e) {
    console.log('Inline drop triggered');
    e.preventDefault();
    e.stopPropagation();
    e.target.classList.remove('drag-over');

    if (draggedProduct) {
      console.log('Adding product via inline handler:', draggedProduct);
      addProductToBundle(draggedProduct);
    }
    return false;
  }

  // Hide flash messages and navigation tabs immediately in builder
  const alerts = document.querySelectorAll('.alert, .alert-success');
  alerts.forEach(alert => {
    alert.style.display = 'none';
  });

  // Hide navigation tabs
  const navTabs = document.querySelectorAll('.nav-tabs, .nav.nav-tabs, ul.nav, .nav');
  navTabs.forEach(nav => {
    if (nav.textContent.includes('Bundles') || nav.textContent.includes('Categories') || nav.textContent.includes('Products') || nav.textContent.includes('Settings')) {
      nav.style.display = 'none';
    }
  });

  // Initialize builder
  console.log('Starting bundle builder initialization...');
  initializeBundleBuilder();

  function initializeBundleBuilder() {
    // Add event listeners
    document.getElementById('saveBundleBtn').addEventListener('click', saveBundle);

    // Initialize store category selection
    initializeStoreCategorySelection();

    // Load initial products (supplements)
    loadStoreProducts('supplements');

    // Initialize drag and drop
    initializeDragAndDrop();

    // Update preview initially
    updateBundlePreview();
  }

  function initializeStoreCategorySelection() {
    document.querySelectorAll('.store-category-card').forEach(card => {
      card.addEventListener('click', function() {
        // Remove active state from all categories
        document.querySelectorAll('.store-category-card').forEach(c => {
          c.classList.remove('active');
        });

        // Add active state to clicked category
        this.classList.add('active');

        // Load products for this category
        const categoryName = this.dataset.category;
        loadStoreProducts(categoryName);

        // Update category name display
        const categoryDisplayName = this.querySelector('.category-name').textContent;
        document.getElementById('selectedCategoryName').textContent = categoryDisplayName;
      });
    });
  }

  function loadStoreProducts(categoryKey) {
    const productsGrid = document.getElementById('storeProductsGrid');
    const products = storeData[categoryKey] || [];

    productsGrid.innerHTML = products.map(product => `
      <div class="store-product-card"
           draggable="true"
           data-product='${JSON.stringify(product)}'>
        <div class="product-emoji">${product.emoji}</div>
        <div class="product-name">${product.name}</div>
        <div class="product-price">$${product.price}</div>
      </div>
    `).join('');

    // Add drag event listeners to new products only
    initializeProductDragEvents();
  }

  function initializeProductDragEvents() {
    // Only initialize drag events for products that don't have them yet
    const productCards = document.querySelectorAll('.store-product-card[draggable="true"]:not([data-drag-initialized])');
    console.log('Initializing drag for new products:', productCards.length);

    productCards.forEach(card => {
      card.addEventListener('dragstart', function(e) {
        console.log('Drag started for:', this.dataset.product);
        e.dataTransfer.setData('text/plain', this.dataset.product);
        this.classList.add('dragging');
        draggedProduct = JSON.parse(this.dataset.product);
      });

      card.addEventListener('dragend', function(e) {
        console.log('Drag ended');
        this.classList.remove('dragging');
        draggedProduct = null;
      });

      // Mark as initialized
      card.setAttribute('data-drag-initialized', 'true');
    });
  }

  let dragDropInitialized = false;

  function initializeDragAndDrop() {
    if (dragDropInitialized) {
      console.log('Drag and drop already initialized, skipping...');
      return;
    }

    console.log('Initializing drag and drop...');

    // Small delay to ensure DOM is ready
    setTimeout(() => {
      // Initialize drag events for initial products
      initializeProductDragEvents();

      // Drop zone events for bundle drop zone
      const bundleDropZone = document.getElementById('bundleDropZone');
      console.log('Found bundle drop zone:', bundleDropZone ? 'YES' : 'NO');
      console.log('Bundle drop zone element:', bundleDropZone);

      if (bundleDropZone) {
        bundleDropZone.addEventListener('dragover', function(e) {
          console.log('Dragover on bundle zone');
          e.preventDefault();
          e.stopPropagation();
          this.classList.add('drag-over');
          return false;
        });

        bundleDropZone.addEventListener('dragleave', function(e) {
          console.log('Dragleave on bundle zone');
          this.classList.remove('drag-over');
        });

        bundleDropZone.addEventListener('drop', function(e) {
          console.log('Drop event triggered on bundle zone');
          e.preventDefault();
          e.stopPropagation();
          this.classList.remove('drag-over');

          if (draggedProduct) {
            console.log('Adding product to bundle:', draggedProduct);
            addProductToBundle(draggedProduct);
          }
          return false;
        });

        console.log('Drop zone event listeners added successfully');
      } else {
        console.error('Bundle drop zone not found!');
      }

      dragDropInitialized = true;
    }, 100); // 100ms delay
  }

  function addBundleCategory() {
    const container = document.getElementById('bundleCategoriesContainer');
    const emptyState = document.getElementById('emptyBundleState');
    const template = document.getElementById('bundleCategoryTemplate');

    // Hide empty state
    if (emptyState) {
      emptyState.style.display = 'none';
    }

    // Clone template
    const categoryElement = template.content.cloneNode(true);
    const categoryCard = categoryElement.querySelector('.bundle-category-card');

    // Set unique index
    categoryCard.setAttribute('data-category-index', bundleCategoryCounter);
    bundleCategoryCounter++;

    // Set default name
    const nameInput = categoryCard.querySelector('.category-name-input');
    nameInput.value = `Category ${bundleCategoryCounter}`;
    nameInput.setAttribute('name', `categories[${bundleCategoryCounter}][name]`);

    // Set form field names
    const requiredCheckbox = categoryCard.querySelector('.category-required');
    requiredCheckbox.setAttribute('name', `categories[${bundleCategoryCounter}][required]`);

    const maxSelectionsInput = categoryCard.querySelector('.category-max-selections');
    maxSelectionsInput.setAttribute('name', `categories[${bundleCategoryCounter}][max_selections]`);

    // Add event listeners
    nameInput.addEventListener('input', updateBundlePreview);
    requiredCheckbox.addEventListener('change', updateBundlePreview);
    maxSelectionsInput.addEventListener('change', updateBundlePreview);

    // Add to container
    container.appendChild(categoryElement);

    // Focus on name input
    const newNameInput = container.querySelector(`[data-category-index="${bundleCategoryCounter-1}"] .category-name-input`);
    if (newNameInput) {
      newNameInput.focus();
    }

    // Update preview
    updateBundlePreview();
  }

  function removeBundleCategory(button) {
    const categoryCard = button.closest('.bundle-category-card');
    if (categoryCard) {
      categoryCard.remove();

      // Show empty state if no categories
      const container = document.getElementById('bundleCategoriesContainer');
      const categories = container.querySelectorAll('.bundle-category-card');
      if (categories.length === 0) {
        const emptyState = document.getElementById('emptyBundleState');
        if (emptyState) {
          emptyState.style.display = 'block';
        }
      }

      updateBundlePreview();
    }
  }

  function addProductToBundle(product) {
    console.log('Adding product to bundle:', product);
    console.log('Current selected products:', selectedProducts);

    // Check if product already exists
    const existingProduct = selectedProducts.find(p => p.id == product.id);
    if (existingProduct) {
      console.log('Product already exists in bundle');
      showNotification(`${product.name} is already in the bundle!`, 'warning');
      return;
    }

    // Add product to selected list
    selectedProducts.push(product);
    console.log('Product added. New selected products:', selectedProducts);

    // Update UI
    updateSelectedProductsDisplay();
    updateBundlePreview();

    showNotification(`${product.name} added to bundle!`, 'success');
  }

  function updateSelectedProductsDisplay() {
    const emptyState = document.getElementById('dropZoneEmpty');
    const grid = document.getElementById('selectedProductsGrid');
    const counter = document.getElementById('selectedProductsCount');

    // Update counter in footer
    if (counter) {
      counter.textContent = `${selectedProducts.length} product${selectedProducts.length !== 1 ? 's' : ''} selected`;
    }

    if (selectedProducts.length === 0) {
      emptyState.style.display = 'flex';
      grid.innerHTML = '';
      return;
    }

    emptyState.style.display = 'none';

    grid.innerHTML = selectedProducts.map(product => `
      <div class="selected-product-card" data-product-id="${product.id}">
        <button type="button" class="remove-product-btn" onclick="removeProductFromBundle(${product.id})">
          ✕
        </button>
        <div class="selected-product-emoji">${product.emoji}</div>
        <div class="selected-product-name">${product.name}</div>
        <div class="selected-product-price">$${product.price}</div>
      </div>
    `).join('');
  }

  function removeProductFromBundle(productId) {
    selectedProducts = selectedProducts.filter(p => p.id !== productId);
    updateSelectedProductsDisplay();
    updateBundlePreview();
    showNotification('Product removed from bundle', 'info');
  }

  function addProductToBundleCategory(dropZone, product) {
    const categoryCard = dropZone.closest('.bundle-category-card');
    const categoryProductsZone = categoryCard.querySelector('.category-products-drop-zone');
    let droppedProductsContainer = categoryProductsZone.querySelector('.dropped-products');

    // Check if product already exists in this category
    const existingProducts = droppedProductsContainer.querySelectorAll('.dropped-product-item');
    for (let existingProduct of existingProducts) {
      if (existingProduct.dataset.productId == product.id) {
        showNotification(`${product.name} is already in this category!`, 'warning');
        return;
      }
    }

    // Hide drop zone and show products container
    dropZone.style.display = 'none';
    droppedProductsContainer.style.display = 'block';

    // Create product element
    const productElement = document.createElement('div');
    productElement.className = 'dropped-product-item';
    productElement.dataset.productId = product.id;
    productElement.innerHTML = `
      <div class="dropped-product-info">
        <div class="dropped-product-name">${product.emoji} ${product.name}</div>
        <div class="dropped-product-price">$${product.price}</div>
      </div>
      <button type="button" class="remove-product-btn" onclick="removeProductFromCategory(this)">
        ✕
      </button>
    `;

    droppedProductsContainer.appendChild(productElement);

    showNotification(`${product.name} added to category! 🎉`, 'success');
    updateBundlePreview();
  }

  function removeProductFromCategory(button) {
    const productItem = button.closest('.dropped-product-item');
    const categoryCard = button.closest('.bundle-category-card');
    const droppedProductsContainer = categoryCard.querySelector('.dropped-products');
    const dropZone = categoryCard.querySelector('.products-drop-zone');

    productItem.remove();

    // If no more products, show drop zone again
    const remainingProducts = droppedProductsContainer.querySelectorAll('.dropped-product-item');
    if (remainingProducts.length === 0) {
      droppedProductsContainer.style.display = 'none';
      dropZone.style.display = 'flex';
    }

    updateBundlePreview();
  }

  function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 3000);
  }

  function updateBundlePreview() {
    const categories = document.querySelectorAll('.bundle-category-card');
    const previewCategories = document.getElementById('previewCategories');
    const categoriesCount = document.getElementById('categoriesCount');
    const productsCount = document.getElementById('productsCount');

    let totalProducts = 0;
    let previewHTML = '';

    if (categories.length === 0) {
      previewHTML = '<small class="text-muted">No categories configured</small>';
    } else {
      categories.forEach(category => {
        const name = category.querySelector('.category-name-input').value || 'Unnamed';
        const required = category.querySelector('.category-required').checked;
        const maxSelections = category.querySelector('.category-max-selections').value;
        const products = category.querySelectorAll('.dropped-product-item');

        totalProducts += products.length;

        previewHTML += `
          <div class="preview-category mb-2">
            <div class="d-flex justify-content-between align-items-center">
              <span class="fw-medium">${name}</span>
              <small class="text-muted">
                ${required ? 'Required' : 'Optional'} • Max: ${maxSelections} • ${products.length} products
              </small>
            </div>
          </div>
        `;
      });
    }

    previewCategories.innerHTML = previewHTML;
    categoriesCount.textContent = categories.length;
    productsCount.textContent = totalProducts;
  }

  function saveBundle() {
    const form = document.getElementById('bundleBuilderForm');
    const categories = document.querySelectorAll('.bundle-category-card');

    // Validate categories
    if (categories.length === 0) {
      showNotification('Please add at least one category before saving.', 'warning');
      return;
    }

    // Validate category names
    let hasEmptyNames = false;
    categories.forEach(category => {
      const nameInput = category.querySelector('.category-name-input');
      if (!nameInput.value.trim()) {
        hasEmptyNames = true;
        nameInput.focus();
        nameInput.classList.add('is-invalid');
      } else {
        nameInput.classList.remove('is-invalid');
      }
    });

    if (hasEmptyNames) {
      showNotification('Please provide names for all categories.', 'warning');
      return;
    }

    // Collect bundle data
    bundleData.categories = [];
    categories.forEach((category, index) => {
      const name = category.querySelector('.category-name-input').value;
      const required = category.querySelector('.category-required').checked;
      const maxSelections = category.querySelector('.category-max-selections').value;
      const products = [];

      category.querySelectorAll('.dropped-product-item').forEach(productItem => {
        const productId = productItem.dataset.productId;
        // Find product data from store data
        for (let categoryKey in storeData) {
          const product = storeData[categoryKey].find(p => p.id == productId);
          if (product) {
            products.push(product);
            break;
          }
        }
      });

      bundleData.categories.push({
        name: name,
        required: required,
        max_selections: parseInt(maxSelections),
        products: products
      });
    });

    // Show loading state
    const saveBtn = document.getElementById('saveBundleBtn');
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Saving...';
    saveBtn.disabled = true;

    // Create hidden input with bundle data
    const hiddenInput = document.createElement('input');
    hiddenInput.type = 'hidden';
    hiddenInput.name = 'categories';
    hiddenInput.value = JSON.stringify(bundleData.categories);
    form.appendChild(hiddenInput);

    // Submit form
    form.submit();
  }

  // Make functions globally available
  window.addBundleCategory = addBundleCategory;
  window.removeBundleCategory = removeBundleCategory;
  window.removeProductFromCategory = removeProductFromCategory;
});

// Global functions for wizard navigation
window.backToWizardStep1 = function() {
  if (confirm('Are you sure you want to go back? Your current progress will be saved.')) {
    // Save current state to session
    sessionStorage.setItem('bundle_builder_state', JSON.stringify({
      selectedProducts: selectedProducts || [],
      bundleData: bundleData || { categories: [] }
    }));

    // Go back to bundle creation form
    window.location.href = '<%= new_admin_bundle_path %>';
  }
};

window.continueToWizardStep3 = function() {
  // Validate that we have some configuration
  if ((!selectedProducts || selectedProducts.length === 0) &&
      (!bundleData.categories || bundleData.categories.length === 0)) {
    alert('Please add some products or configure categories before continuing.');
    return;
  }

  // Save current state to session
  sessionStorage.setItem('bundle_builder_state', JSON.stringify({
    selectedProducts: selectedProducts || [],
    bundleData: bundleData || { categories: [] }
  }));

  // Continue to Step 3 (Review)
  window.location.href = '<%= bundle_wizard_step_admin_bundles_path(step: "review") %>';
};
</script>
