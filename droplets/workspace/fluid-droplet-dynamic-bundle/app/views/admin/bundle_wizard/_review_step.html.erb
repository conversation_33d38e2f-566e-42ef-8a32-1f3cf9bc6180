<!-- Step 3/3: Review & Save -->
<div class="step-header">
  <h2>🔍 Review Your Bundle</h2>
  <p>Review your bundle configuration and create it in Fluid</p>
</div>

<div class="step-body">
  <!-- Bundle Info Summary -->
  <div class="bundle-info">
    <h5>📦 Bundle Information</h5>
    <p><strong>Name:</strong> <%= @bundle_draft['name'] %></p>
    <p><strong>SKU:</strong> <%= @bundle_draft['sku'] %></p>
    <% if @bundle_draft['description'].present? %>
      <p><strong>Description:</strong> <%= @bundle_draft['description'] %></p>
    <% end %>
  </div>

  <!-- Selected Categories Summary -->
  <div class="review-section">
    <h5>🏪 Selected Categories</h5>
    <% if @selected_categories.any? %>
      <div class="categories-summary">
        <% @selected_categories.each do |category_id| %>
          <% category = @available_categories.find { |cat| cat[:id] == category_id } %>
          <% if category %>
            <div class="category-summary-card">
              <div class="category-icon"><%= category[:icon] %></div>
              <div class="category-info">
                <h6><%= category[:name] %></h6>
                <small class="text-muted"><%= category[:description] %></small>
              </div>
            </div>
          <% end %>
        <% end %>
      </div>
    <% else %>
      <p class="text-muted">No categories selected</p>
    <% end %>
  </div>

  <!-- Selected Products Summary -->
  <div class="review-section">
    <h5>📦 Selected Products</h5>
    <% if @selected_products.any? %>
      <% @selected_products.each do |category_id, products| %>
        <% category = @available_categories.find { |cat| cat[:id] == category_id } %>
        <% if category && products.any? %>
          <div class="category-products-review">
            <h6><%= category[:icon] %> <%= category[:name] %></h6>
            <div class="products-grid-review">
              <% products.each do |product_id| %>
                <% product = @category_products[category_id]&.find { |p| p[:id] == product_id } %>
                <% if product %>
                  <div class="product-review-card">
                    <div class="product-name"><%= product[:name] %></div>
                    <div class="product-price"><%= product[:price] %></div>
                  </div>
                <% end %>
              <% end %>
            </div>
          </div>
        <% end %>
      <% end %>
    <% else %>
      <p class="text-muted">No products selected</p>
    <% end %>
  </div>

  <!-- Bundle Statistics -->
  <div class="review-section">
    <h5>📊 Bundle Statistics</h5>
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-number"><%= @selected_categories.length %></div>
        <div class="stat-label">Categories</div>
      </div>
      <div class="stat-card">
        <div class="stat-number"><%= @selected_products.values.flatten.length %></div>
        <div class="stat-label">Products</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">Draft</div>
        <div class="stat-label">Status</div>
      </div>
    </div>
  </div>

  <!-- Error Display Area -->
  <div id="bundleCreationError" class="alert alert-danger" style="display: none;">
    <h6 class="alert-heading">❌ Bundle Creation Failed</h6>
    <p id="errorMessage"></p>
    <button type="button" class="btn btn-outline-danger btn-sm" onclick="retryBundleCreation()">
      🔄 Retry
    </button>
  </div>

  <!-- Success Display Area -->
  <div id="bundleCreationSuccess" class="alert alert-success" style="display: none;">
    <h6 class="alert-heading">🎉 Bundle Created Successfully!</h6>
    <p>Your bundle has been created in Fluid and is ready to use.</p>
  </div>
</div>

<div class="wizard-actions">
  <div>
    <%= link_to "← Back to Builder", bundle_wizard_step_admin_bundles_path(step: 'products'), 
        class: "btn btn-outline-secondary" %>
  </div>
  <div class="d-flex gap-2">
    <%= link_to "Cancel", admin_bundles_path, 
        class: "btn btn-outline-danger",
        onclick: "return confirm('Are you sure you want to cancel? All progress will be lost.')" %>
    
    <button type="button" 
            class="btn btn-success btn-lg" 
            id="createBundleBtn"
            onclick="createFinalBundle()">
      <span id="createBtnIcon">🚀</span>
      <span id="createBtnText">Create Bundle</span>
    </button>
  </div>
</div>

<style>
.categories-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.category-summary-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.category-icon {
  font-size: 24px;
}

.category-info h6 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
}

.category-products-review {
  margin-bottom: 25px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.category-products-review h6 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 16px;
}

.products-grid-review {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
}

.product-review-card {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 12px;
  text-align: center;
}

.product-name {
  font-weight: 600;
  color: #495057;
  font-size: 13px;
  margin-bottom: 4px;
}

.product-price {
  color: #28a745;
  font-weight: 500;
  font-size: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-top: 15px;
}

.stat-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #007bff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

.review-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.review-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.review-section h5 {
  color: #495057;
  margin-bottom: 15px;
  font-size: 18px;
}

#createBundleBtn.loading {
  pointer-events: none;
  opacity: 0.7;
}

.btn-gap {
  gap: 10px;
}
</style>

<script>
// Global function to create the final bundle
window.createFinalBundle = function() {
  const createBtn = document.getElementById('createBundleBtn');
  const btnIcon = document.getElementById('createBtnIcon');
  const btnText = document.getElementById('createBtnText');
  const errorDiv = document.getElementById('bundleCreationError');
  const successDiv = document.getElementById('bundleCreationSuccess');
  
  // Hide previous messages
  errorDiv.style.display = 'none';
  successDiv.style.display = 'none';
  
  // Show loading state
  createBtn.classList.add('loading');
  btnIcon.textContent = '⏳';
  btnText.textContent = 'Creating Bundle...';
  
  // Get bundle data from session/form
  const bundleData = {
    name: '<%= j @bundle_draft["name"] %>',
    sku: '<%= j @bundle_draft["sku"] %>',
    description: '<%= j @bundle_draft["description"] %>',
    categories: <%= raw @selected_categories.to_json %>,
    products: <%= raw @selected_products.to_json %>
  };
  
  // Call backend to create bundle in Fluid
  fetch('<%= complete_bundle_wizard_admin_bundles_path %>', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({ bundle: bundleData })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // Show success message
      successDiv.style.display = 'block';
      btnIcon.textContent = '✅';
      btnText.textContent = 'Bundle Created!';
      
      // Redirect to index after 2 seconds
      setTimeout(() => {
        window.location.href = '<%= admin_bundles_path %>';
      }, 2000);
    } else {
      throw new Error(data.error || 'Unknown error occurred');
    }
  })
  .catch(error => {
    console.error('Bundle creation failed:', error);
    
    // Show error message
    document.getElementById('errorMessage').textContent = error.message;
    errorDiv.style.display = 'block';
    
    // Reset button
    createBtn.classList.remove('loading');
    btnIcon.textContent = '🚀';
    btnText.textContent = 'Create Bundle';
  });
};

// Global function to retry bundle creation
window.retryBundleCreation = function() {
  document.getElementById('bundleCreationError').style.display = 'none';
  createFinalBundle();
};
</script>
