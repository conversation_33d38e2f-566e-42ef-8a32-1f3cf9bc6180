<!-- Step 1: Bundle Information -->
<div class="step-content">

  <%= form_with model: @wizard, url: process_bundle_wizard_step_admin_bundles_path(step: 'info'),
                method: :post,
                data: {
                  turbo_frame: "wizard_step",
                  bundle_wizard_target: "form",
                  action: "turbo:submit-end->bundle-wizard#stepSubmitted turbo:submit-error->bundle-wizard#stepError"
                },
                class: "needs-validation",
                novalidate: true do |form| %>
    
    <div class="row justify-content-center">
      <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
          <div class="card-body p-4">
            
            <!-- Bundle Name -->
            <div class="mb-4">
              <%= form.label :name, "Bundle Name", class: "form-label fw-semibold" %>
              <%= form.text_field :name, 
                    class: "form-control form-control-lg #{'is-invalid' if @bundle&.errors&.[](:name)&.any?}",
                    placeholder: "e.g., Yoli Transformation Bundle",
                    required: true %>
              <% if @bundle&.errors&.[](:name)&.any? %>
                <div class="invalid-feedback">
                  <%= @bundle.errors[:name].first %>
                </div>
              <% end %>
              <div class="form-text">
                This will be the display name for your bundle that customers will see.
              </div>
            </div>

            <!-- Bundle SKU -->
            <div class="mb-4">
              <%= form.label :sku, "SKU (Stock Keeping Unit)", class: "form-label fw-semibold" %>
              <%= form.text_field :sku, 
                    class: "form-control form-control-lg #{'is-invalid' if @bundle&.errors&.[](:sku)&.any?}",
                    placeholder: "e.g., YOLI-TRANS-001",
                    required: true %>
              <% if @bundle&.errors&.[](:sku)&.any? %>
                <div class="invalid-feedback">
                  <%= @bundle.errors[:sku].first %>
                </div>
              <% end %>
              <div id="sku-suggestion" class="sku-suggestion" style="display: none;"></div>
              <div class="form-text">
                Unique identifier for this bundle. Use uppercase letters, numbers, and hyphens only.
              </div>
            </div>

            <!-- Bundle Description -->
            <div class="mb-4">
              <%= form.label :description, "Description", class: "form-label fw-semibold" %>
              <%= form.text_area :description, 
                    class: "form-control #{'is-invalid' if @bundle&.errors&.[](:description)&.any?}",
                    rows: 4,
                    placeholder: "Describe what this bundle offers and its benefits..." %>
              <% if @bundle&.errors&.[](:description)&.any? %>
                <div class="invalid-feedback">
                  <%= @bundle.errors[:description].first %>
                </div>
              <% end %>
              <div class="form-text">
                Optional description that will help customers understand this bundle (max 500 characters).
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>

    <!-- Navigation -->
    <div class="wizard-navigation mt-4 text-center">
      <%= link_to admin_bundles_path, class: "btn btn-outline-secondary me-3" do %>
        <span class="me-1">←</span>Cancel
      <% end %>

      <button type="submit"
              class="btn btn-primary btn-lg"
              data-bundle-wizard-target="nextButton"
              data-action="click->bundle-wizard#nextStep">
        Continue to Builder <span class="ms-1">→</span>
      </button>
    </div>

  <% end %>
</div>

<style>
.step-content {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.step-header h3 {
  color: #2c3e50;
  font-weight: 600;
}

.card {
  border-radius: 12px;
}

.form-control-lg {
  padding: 0.75rem 1rem;
  font-size: 1.1rem;
}

.form-label.fw-semibold {
  color: #495057;
  margin-bottom: 0.5rem;
}

.wizard-navigation {
  padding: 2rem 0;
}

.btn-lg {
  padding: 0.75rem 2rem;
  font-size: 1.1rem;
}

.sku-suggestion {
  font-size: 0.875rem;
  color: #6c757d;
  margin-top: 0.25rem;
  margin-bottom: 0.5rem;
}

.sku-suggestion button {
  background: none;
  border: none;
  color: #007bff;
  text-decoration: underline;
  cursor: pointer;
  padding: 0;
  font-size: inherit;
}

.sku-suggestion button:hover {
  color: #0056b3;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const nameField = document.getElementById('bundle_wizard_name');
  const skuField = document.getElementById('bundle_wizard_sku');
  const skuSuggestion = document.getElementById('sku-suggestion');

  // Debug: Check if elements exist
  console.log('Name field:', nameField);
  console.log('SKU field:', skuField);
  console.log('SKU suggestion:', skuSuggestion);

  let userEditedSku = false;

  // Only proceed if all elements exist
  if (!nameField || !skuField || !skuSuggestion) {
    console.error('SKU suggestion: Required elements not found');
    return;
  }

  // Track if user manually edited SKU
  skuField.addEventListener('input', function() {
    console.log('SKU field edited:', this.value);
    userEditedSku = true;
    hideSuggestion();
  });

  // Generate SKU suggestion when name changes
  nameField.addEventListener('input', function() {
    console.log('Name field changed:', this.value);
    if (!userEditedSku && this.value.trim()) {
      const suggestedSku = generateSku(this.value);
      console.log('Generated SKU:', suggestedSku);
      showSuggestion(suggestedSku);
    } else if (!this.value.trim()) {
      hideSuggestion();
    }
  });

  function generateSku(name) {
    return name
      .trim()
      .toUpperCase()
      .replace(/[^A-Z0-9\s]/g, '') // Remove special characters
      .replace(/\s+/g, '-')        // Replace spaces with hyphens
      .substring(0, 20)            // Limit length
      + '-001';                    // Add suffix
  }

  function showSuggestion(sku) {
    skuSuggestion.innerHTML = `
      <span class="text-muted">Suggested: </span>
      <button type="button" onclick="useSuggestedSku('${sku}')">${sku}</button>
    `;
    skuSuggestion.style.display = 'block';
  }

  function hideSuggestion() {
    skuSuggestion.style.display = 'none';
  }

  // Global function to use suggested SKU
  window.useSuggestedSku = function(sku) {
    skuField.value = sku;
    hideSuggestion();
    userEditedSku = false; // Allow new suggestions
    skuField.focus();
  };
});
</script>
