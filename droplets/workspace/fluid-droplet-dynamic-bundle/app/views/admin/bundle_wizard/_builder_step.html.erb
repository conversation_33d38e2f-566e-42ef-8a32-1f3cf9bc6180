<!-- Step 2: Bundle Builder (Full Page) -->
<% Rails.logger.info("🔥 BUILDER VIEW: Controller: #{controller.class.name}") %>
<% Rails.logger.info("🔥 BUILDER VIEW: Action: #{action_name}") %>
<% Rails.logger.info("🔥 BUILDER VIEW: Params: #{params.inspect}") %>
<% Rails.logger.info("🔥 BUILDER VIEW: @current_step: #{@current_step}") %>
<% Rails.logger.info("🔥 BUILDER VIEW: Wizard state: #{@wizard.aasm.current_state}") %>
<% Rails.logger.info("🔥 BUILDER VIEW: Wizard current_step: #{@wizard.current_step}") %>
<% Rails.logger.info("🔥 BUILDER VIEW: URL step param: #{params[:step]}") %>
<div class="bundle-builder-container"
     data-controller="bundle-builder"
     data-bundle-builder-selected-category-value=""
     data-bundle-builder-bundle-products-value="[]">

<form id="builder-form"
      action="<%= process_bundle_wizard_step_admin_bundles_path(step: 'builder') %>"
      method="post"
      accept-charset="UTF-8"
      data-turbo-frame="wizard_step"
      data-bundle-wizard-target="form">
  <%= hidden_field_tag :authenticity_token, form_authenticity_token %>
  <%= hidden_field_tag :_method, 'post' %>
  <%= hidden_field_tag :builder_config, '{}', id: 'builder_config_input' %>
  <!-- Builder Header -->
  <div class="builder-header-minimal">
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <h6 class="mb-0">
          <span class="me-2">🏗️</span>Building: <strong><%= @wizard.name %></strong>
        </h6>
      </div>
      <div>
        <span class="text-muted me-3">Products: <span class="badge bg-primary" id="productsCount">0</span></span>
      </div>
    </div>
  </div>

    <!-- Hidden field to store builder configuration -->

    <!-- Builder Interface -->
    <div class="builder-main-container">
      <!-- Left Half - Store Catalog -->
      <div class="builder-left-half">
        <div class="categories-section">
          <div class="section-header">
            <h6><span class="me-2">🏪</span>Store Categories</h6>
          </div>
          <div class="store-categories-container">
            <div class="categories-grid" data-bundle-builder-target="categoryGrid">
              <% if @categories.present? %>
                <% @categories.each_with_index do |category, index| %>
                  <div class="store-category-card category-card <%= 'active' if index == 0 %>"
                       data-category-id="<%= category['id'] %>"
                       data-category-name="<%= category['name'] %>"
                       data-products='<%= category['products'].to_json %>'
                       data-action="click->bundle-builder#selectCategory">
                    <div class="category-name-only"><%= category['name'] %></div>
                  </div>
                <% end %>
              <% else %>
                <div class="text-center py-4 text-muted">
                  <div class="mb-2">📦</div>
                  <div>No categories found</div>
                  <small>Check your Fluid API connection</small>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <div class="products-section">
          <div class="section-header">
            <h6><span class="me-2">📦</span>Products <small class="text-muted">Supplements</small></h6>
          </div>
          <div class="store-products-container">
            <div class="products-grid"
                 id="productsGrid"
                 data-bundle-builder-target="productGrid">
              <!-- Products will be loaded here -->
              <div class="loading-products text-center py-4">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Loading products...</span>
                </div>
                <p class="text-muted mt-2">Loading products...</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Half - Bundle Drop Zone -->
      <div class="builder-right-half">
        <div class="section-header">
          <h6><span class="me-2">📦</span>Bundle Products <small class="text-muted">(Drop products here)</small></h6>
        </div>

        <div class="bundle-drop-zone-full"
             id="bundleDropZone"
             data-bundle-builder-target="dropZone">
          <div class="drop-zone-empty"
               id="dropZoneEmpty"
               data-bundle-builder-target="emptyState">
            <div class="drop-zone-icon">📦</div>
            <div class="drop-zone-text">Drop products here to add to bundle</div>
            <small class="text-muted">Drag products from the left panel</small>
          </div>
          <div class="selected-products-grid"
               id="selectedProductsGrid"
               data-bundle-builder-target="selectedProducts"></div>
        </div>
      </div>
    </div>

    <!-- Navigation removed - using navigation outside form -->

</div>
</form>

<!-- Navigation outside form -->
<div style="padding: 20px; text-align: center; background: #f8f9fa; border-top: 1px solid #dee2e6;">
  <button type="button"
          class="btn btn-outline-secondary me-3"
          data-bundle-wizard-target="prevButton"
          data-action="click->bundle-wizard#prevStep">
    <span class="me-1">←</span>Back to Info
  </button>

  <button type="button"
          class="btn btn-primary btn-lg"
          data-bundle-wizard-target="nextButton"
          data-action="click->bundle-wizard#nextStep">
    Continue to Preview <span class="ms-1">→</span>
  </button>

  <a href="/admin/bundles" class="btn btn-success btn-sm ms-2">
    🏠 Go to Bundles List
  </a>
</div>

<style>
.bundle-builder-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.builder-header-minimal {
  padding: 6px 15px;
  min-height: 35px;
  background: white;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.builder-main-container {
  display: flex;
  flex: 1;
  overflow: hidden;
  height: calc(100% - 35px);
}

.builder-left-half, .builder-right-half {
  width: 50%;
  display: flex;
  flex-direction: column;
}

.builder-left-half {
  border-right: 1px solid #e9ecef;
}

.categories-section {
  height: 40%;
  padding: 8px;
  border-bottom: 1px solid #e9ecef;
  overflow-y: auto;
}

.products-section {
  height: 60%;
  padding: 8px;
  overflow-y: auto;
}

.section-header {
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #f0f0f0;
}

.section-header h6 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 0.4rem;
}

.store-category-card {
  aspect-ratio: 1;
  padding: 0.5rem;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  min-height: 80px;
  max-width: 120px;
}

.store-category-card:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.store-category-card.active {
  background: #e3f2fd;
  border-color: #2196f3;
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
}

.category-name-only {
  font-size: 1rem;
  font-weight: 700;
  color: #000;
  line-height: 1.2;
  word-break: break-word;
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
  background: rgba(255,255,255,0.9);
  padding: 2px 4px;
  border-radius: 4px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 0.75rem;
}

.bundle-drop-zone-full {
  flex: 1;
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  padding: 8px;
  margin: 8px;
  background: #f8f9fa;
  overflow-y: auto;
  position: relative;
}

.drop-zone-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 6px;
  height: 120px;
  text-align: center;
}

.drop-zone-icon {
  font-size: 48px;
  opacity: 0.5;
}

.drop-zone-text {
  font-size: 16px;
  font-weight: 600;
  color: #6c757d;
}

.wizard-navigation {
  padding: 6px 15px;
  background: white;
  border-top: 1px solid #e9ecef;
  flex-shrink: 0;
  min-height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-card {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  cursor: grab;
  transition: all 0.2s;
}

.product-card:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.product-card:active {
  cursor: grabbing;
}

.product-emoji {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.product-name {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.product-price {
  color: #28a745;
  font-weight: 600;
}

/* Drag & Drop Styles */
.product-card.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.bundle-drop-zone-full.drag-over {
  border-color: #28a745;
  background-color: #f8fff8;
}

.bundle-product-card {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  margin-bottom: 0.5rem;
  background: white;
}

.bundle-product-info {
  flex: 1;
}

.bundle-product-name {
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.bundle-product-price {
  font-size: 0.75rem;
  color: #28a745;
  font-weight: 600;
}
</style>

<!-- All JavaScript functionality moved to Stimulus controllers -->
