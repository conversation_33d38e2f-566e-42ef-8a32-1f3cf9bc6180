<!-- Step 3: Preview & Save -->
<div class="step-content">
  <div class="step-header text-center mb-4">
    <h3><span class="me-2">👀</span>Review Your Bundle</h3>
    <p class="text-muted">Review your bundle configuration before saving it to your store.</p>
  </div>

  <div class="row justify-content-center">
    <div class="col-lg-10">
      
      <!-- Bundle Overview Card -->
      <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">
            <span class="me-2">📦</span>Bundle Overview
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-8">
              <h4 class="text-primary"><%= @wizard.name %></h4>
              <p class="text-muted mb-2">
                <strong>SKU:</strong> <%= @wizard.sku %>
              </p>
              <% if @wizard.description.present? %>
                <p class="mb-0"><%= @wizard.description %></p>
              <% end %>
            </div>
            <div class="col-md-4 text-end">
              <div class="bundle-stats">
                <div class="stat-item mb-2">
                  <span class="badge bg-info fs-6">
                    <%= (@wizard.categories || []).length %> Categories
                  </span>
                </div>
                <div class="stat-item">
                  <span class="badge bg-success fs-6">
                    <%= (@wizard.products || []).length %> Products
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Categories & Products Preview -->
      <% if (@wizard.categories || []).any? %>
        <div class="card border-0 shadow-sm mb-4">
          <div class="card-header">
            <h6 class="mb-0">
              <span class="me-2">🏷️</span>Categories & Products
            </h6>
          </div>
          <div class="card-body">
            <% (@wizard.categories || []).each_with_index do |category, index| %>
              <div class="category-preview mb-3 <%= 'border-bottom pb-3' unless index == (@wizard.categories.length - 1) %>">
                <h6 class="text-primary">
                  <span class="me-2">📂</span><%= category['name'] || "Category #{index + 1}" %>
                </h6>
                <% if (category['products'] || []).any? %>
                  <div class="products-list">
                    <% category['products'].each do |product| %>
                      <span class="badge bg-light text-dark me-2 mb-1">
                        <%= product['name'] || product['id'] %>
                      </span>
                    <% end %>
                  </div>
                <% else %>
                  <p class="text-muted small mb-0">No products assigned to this category</p>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>

      <!-- All Products Preview (if no categories) -->
      <% if (@wizard.categories || []).empty? && (@wizard.products || []).any? %>
        <div class="card border-0 shadow-sm mb-4">
          <div class="card-header">
            <h6 class="mb-0">
              <span class="me-2">📦</span>Bundle Products
            </h6>
          </div>
          <div class="card-body">
            <div class="products-list">
              <% (@wizard.products || []).each do |product| %>
                <span class="badge bg-primary me-2 mb-2">
                  <%= product['name'] || product['id'] %>
                </span>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Empty State -->
      <% if (@wizard.categories || []).empty? && (@wizard.products || []).empty? %>
        <div class="card border-0 shadow-sm mb-4">
          <div class="card-body text-center py-5">
            <div class="empty-state">
              <div style="font-size: 48px; color: #ccc; margin-bottom: 20px;">📦</div>
              <h5>No Products Configured</h5>
              <p class="text-muted">
                Your bundle doesn't have any products yet. You can still save it and configure products later.
              </p>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Configuration Summary -->
      <div class="card border-0 shadow-sm mb-4">
        <div class="card-header">
          <h6 class="mb-0">
            <span class="me-2">⚙️</span>Configuration Summary
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <ul class="list-unstyled mb-0">
                <li><strong>Bundle Type:</strong> Dynamic Bundle</li>
                <li><strong>Created Via:</strong> Wizard</li>
                <li><strong>Status:</strong> <span class="badge bg-warning">Draft</span></li>
              </ul>
            </div>
            <div class="col-md-6">
              <ul class="list-unstyled mb-0">
                <li><strong>Categories:</strong> <%= (@wizard.categories || []).length %></li>
                <li><strong>Products:</strong> <%= (@wizard.products || []).length %></li>
                <li><strong>Created:</strong> <%= Time.current.strftime("%B %d, %Y") %></li>
              </ul>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>

  <!-- Navigation -->
  <div class="wizard-navigation mt-4 text-center">
    <button type="button"
            class="btn btn-outline-secondary me-3"
            data-bundle-wizard-target="prevButton"
            data-action="click->bundle-wizard#prevStep">
      <span class="me-1">←</span>Back to Builder
    </button>

    <%= button_to complete_bundle_wizard_admin_bundles_path,
                  method: :post,
                  class: "btn btn-success btn-lg",
                  style: "display: inline;",
                  data: {
                    turbo_method: :post,
                    confirm: "Create this bundle?"
                  } do %>
      <span class="me-1">✅</span>Complete Bundle
    <% end %>
  </div>
</div>

<style>
.step-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.step-header h3 {
  color: #2c3e50;
  font-weight: 600;
}

.card {
  border-radius: 12px;
}

.bundle-stats .stat-item {
  display: block;
}

.category-preview h6 {
  margin-bottom: 0.5rem;
}

.products-list .badge {
  font-size: 0.85rem;
  padding: 0.5rem 0.75rem;
}

.empty-state {
  color: #6c757d;
}

.wizard-navigation {
  padding: 2rem 0;
}

.btn-lg {
  padding: 0.75rem 2rem;
  font-size: 1.1rem;
}
</style>
