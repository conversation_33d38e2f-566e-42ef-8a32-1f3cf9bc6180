class User < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable

  # Admin role methods
  def admin?
    has_permission_set?('admin') || has_permission_set?('bundle_admin')
  end

  def bundle_admin?
    has_permission_set?('bundle_admin')
  end

  def make_admin!
    add_permission_set('admin')
  end

  def make_bundle_admin!
    add_permission_set('bundle_admin')
  end

  def remove_admin!
    remove_permission_set('admin')
    remove_permission_set('bundle_admin')
  end

  def has_permission_set?(set_name)
    permission_sets.include?(set_name)
  end

  def add_permission_set(set_name)
    self.permission_sets = (permission_sets + [ set_name ]).uniq
    save
  end

  def remove_permission_set(set_name)
    self.permission_sets = permission_sets - [ set_name ]
    save
  end
end
