# frozen_string_literal: true

# Bundle model for managing dynamic product bundles
# This model handles validation and business logic for bundles
class Bundle
  include ActiveModel::Model
  include ActiveModel::Attributes
  include ActiveModel::Validations

  # Bundle attributes
  attribute :id, :integer
  attribute :name, :string
  attribute :sku, :string
  attribute :description, :string
  attribute :status, :string, default: 'draft'
  attribute :created_at, :datetime
  attribute :updated_at, :datetime

  # Categories as a custom attribute with default empty array
  def categories
    @categories ||= []
  end

  def categories=(value)
    @categories = Array(value)
  end

  # Validations
  validates :name, presence: true, length: { minimum: 3, maximum: 100 }
  validates :sku, presence: true, length: { minimum: 3, maximum: 50 }
  validates :sku, format: { 
    with: /\A[A-Z0-9\-]+\z/, 
    message: "can only contain uppercase letters, numbers, and hyphens" 
  }
  validates :status, inclusion: { in: %w[draft active inactive] }
  validates :description, length: { maximum: 500 }

  # Custom validation for SKU uniqueness (would need to check against Fluid API)
  validate :sku_uniqueness

  # Status helpers
  def draft?
    status == 'draft'
  end

  def active?
    status == 'active'
  end

  def inactive?
    status == 'inactive'
  end

  # Generate SKU from name
  def self.generate_sku_from_name(name)
    return '' if name.blank?
    
    base_sku = name
      .upcase
      .gsub(/[^A-Z0-9\s]/, '')
      .gsub(/\s+/, '-')
      .slice(0, 17) # Leave room for -001 suffix
    
    "#{base_sku}-001"
  end

  # Check if bundle has categories configured
  def has_categories?
    categories.present? && categories.any?
  end

  # Get category count
  def category_count
    categories&.size || 0
  end

  # Simulate persistence methods for compatibility with Rails forms
  def persisted?
    id.present?
  end

  def new_record?
    !persisted?
  end

  def to_param
    id.to_s if id
  end

  private

  def sku_uniqueness
    return unless sku.present?

    # Skip validation in test environment to avoid API calls
    return if Rails.env.test?

    # Skip validation if this is an existing bundle (update scenario)
    return if persisted?

    begin
      # Check if SKU already exists in Fluid API
      # We'll search for products/bundles with this SKU
      result = check_sku_availability(sku)

      if result[:exists]
        errors.add(:sku, "has already been taken. Found existing #{result[:type]}: '#{result[:name]}'")
      end
    rescue => e
      Rails.logger.warn("SKU uniqueness check failed: #{e.message}")
      # In case of API failure, we'll allow the SKU but log the issue
      # This prevents the form from breaking due to API issues
    end
  end

  # Check if SKU is available by searching Fluid API
  def check_sku_availability(sku_to_check)
    # Try to get company context from current thread or fallback
    company = Thread.current[:current_company] || Company.first

    unless company
      Rails.logger.warn("No company context available for SKU validation")
      return { exists: false }
    end

    # Search for existing products with this SKU
    products_result = Fluid::ProductsService.call(
      action: :list,
      page: 1,
      per_page: 1,
      search: sku_to_check,
      company: company
    )

    if products_result.success?
      products = products_result.data[:products] || []
      exact_match = products.find { |p| p['sku']&.upcase == sku_to_check.upcase }

      if exact_match
        return {
          exists: true,
          type: 'product',
          name: exact_match['name'] || 'Unknown Product'
        }
      end
    end

    # Search for existing bundles with this SKU
    bundles_result = Fluid::BundlesService.call(
      action: :list,
      page: 1,
      per_page: 1,
      company: company
    )

    if bundles_result.success?
      bundles = bundles_result.data[:bundles] || []
      exact_match = bundles.find { |b| b['sku']&.upcase == sku_to_check.upcase }

      if exact_match
        return {
          exists: true,
          type: 'bundle',
          name: exact_match['name'] || 'Unknown Bundle'
        }
      end
    end

    # SKU is available
    { exists: false }
  end
end
