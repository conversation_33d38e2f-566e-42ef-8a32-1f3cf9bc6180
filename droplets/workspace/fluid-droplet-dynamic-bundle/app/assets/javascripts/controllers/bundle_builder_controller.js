// Bundle Builder Controller
// Handles drag & drop product selection and bundle building
export default class BundleBuilderController {
  static targets = [
    "categoryGrid", 
    "productGrid", 
    "dropZone", 
    "selectedProducts",
    "emptyState",
    "productCount"
  ]
  static values = { 
    selectedCategory: String,
    bundleProducts: Array
  }

  connect() {
    console.log("🏗️ Bundle Builder Controller connected")
    console.log("Selected category:", this.selectedCategoryValue)
    console.log("Bundle products:", this.bundleProductsValue)

    this.initializeDragAndDrop()
    this.updateProductCount()
    this.updateEmptyState()

    // Load first category products automatically
    this.loadFirstCategory()
  }

  // Load the first category's products on initialization
  loadFirstCategory() {
    const firstCategory = this.categoryGridTarget.querySelector('.store-category-card.active')
    if (firstCategory) {
      const categoryId = firstCategory.dataset.categoryId
      const categoryName = firstCategory.dataset.categoryName
      console.log(`🎯 Auto-loading first category: ${categoryName}`)
      this.selectedCategoryValue = categoryId
      this.loadCategoryProducts(categoryId)
    }
  }

  // Initialize drag and drop functionality
  initializeDragAndDrop() {
    console.log("🎯 Initializing drag and drop")
    
    // Setup product cards as draggable
    this.setupDraggableProducts()
    
    // Setup drop zone
    this.setupDropZone()
  }

  // Setup product cards as draggable
  setupDraggableProducts() {
    const productCards = this.productGridTarget.querySelectorAll('.product-card')
    
    productCards.forEach(card => {
      card.draggable = true
      
      card.addEventListener('dragstart', (e) => {
        console.log('🎯 Drag started:', card.dataset.productName)
        e.dataTransfer.setData('text/plain', JSON.stringify({
          id: card.dataset.productId,
          name: card.dataset.productName,
          price: card.dataset.productPrice,
          image: card.dataset.productImage
        }))
        card.classList.add('dragging')
      })
      
      card.addEventListener('dragend', (e) => {
        console.log('🎯 Drag ended')
        card.classList.remove('dragging')
      })
    })
  }

  // Setup drop zone for products
  setupDropZone() {
    if (!this.hasDropZoneTarget) {
      console.log('❌ Drop zone not found')
      return
    }

    const dropZone = this.dropZoneTarget
    
    dropZone.addEventListener('dragover', (e) => {
      e.preventDefault()
      dropZone.classList.add('drag-over')
    })
    
    dropZone.addEventListener('dragleave', (e) => {
      if (!dropZone.contains(e.relatedTarget)) {
        dropZone.classList.remove('drag-over')
      }
    })
    
    dropZone.addEventListener('drop', (e) => {
      e.preventDefault()
      dropZone.classList.remove('drag-over')
      
      try {
        const productData = JSON.parse(e.dataTransfer.getData('text/plain'))
        console.log('🎯 Product dropped:', productData.name)
        this.addProductToBundle(productData)
      } catch (error) {
        console.error('❌ Error dropping product:', error)
      }
    })
  }

  // Handle category selection
  selectCategory(event) {
    const categoryButton = event.currentTarget
    const categoryId = categoryButton.dataset.categoryId
    const categoryName = categoryButton.dataset.categoryName
    
    console.log(`📂 Category selected: ${categoryName} (${categoryId})`)
    
    // Update selected category
    this.selectedCategoryValue = categoryId
    
    // Update UI
    this.updateCategorySelection(categoryButton)
    
    // Load products for this category
    this.loadCategoryProducts(categoryId)
  }

  // Update category selection UI
  updateCategorySelection(selectedButton) {
    // Remove active class from all category buttons
    this.categoryGridTarget.querySelectorAll('.category-card').forEach(card => {
      card.classList.remove('active', 'border-primary')
    })
    
    // Add active class to selected button
    selectedButton.classList.add('active', 'border-primary')
  }

  // Load products for selected category
  loadCategoryProducts(categoryId) {
    console.log(`📦 Loading products for category: ${categoryId}`)

    // Get products from the category element's data attribute
    const categoryElement = this.categoryGridTarget.querySelector(`[data-category-id="${categoryId}"]`)
    if (!categoryElement) {
      console.error('❌ Category element not found')
      return
    }

    let products = []
    try {
      products = JSON.parse(categoryElement.dataset.products || '[]')
      console.log('📦 Products found:', products)
    } catch (error) {
      console.error('❌ Error parsing products:', error)
      products = []
    }

    this.renderProducts(products)
  }

  // Render products from category data
  renderProducts(products) {
    // Show loading state
    this.productGridTarget.innerHTML = `
      <div class="loading-products text-center py-4">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading products...</span>
        </div>
        <p class="mt-2 text-muted">Loading products...</p>
      </div>
    `

    // Simulate loading delay for better UX
    setTimeout(() => {
      if (products && products.length > 0) {
        this.productGridTarget.innerHTML = products.map(product => `
          <div class="product-card"
               draggable="true"
               data-product-id="${product.id}"
               data-product-name="${product.title || product.name}"
               data-product-price="${product.price_in_currency || product.price}"
               data-product-image="${product.image_url || ''}">
            <div class="product-image">
              ${product.image_url ?
                `<img src="${product.image_url}" alt="${product.title}" style="width: 100%; height: 60px; object-fit: cover; border-radius: 4px;">` :
                '<div style="width: 100%; height: 60px; background: #f8f9fa; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 24px;">📦</div>'
              }
            </div>
            <div class="product-name">${product.title || product.name}</div>
            <div class="product-price">${product.price_in_currency || product.price}</div>
          </div>
        `).join('')
      } else {
        this.productGridTarget.innerHTML = `
          <div class="text-center py-4 text-muted">
            <div>No products in this category</div>
          </div>
        `
      }

      // Re-initialize drag and drop for new products
      this.setupDraggableProducts()
    }, 500)
  }

  // Add product to bundle
  addProductToBundle(product) {
    // Check if product already exists
    if (this.bundleProductsValue.some(p => p.id === product.id)) {
      console.log('⚠️ Product already in bundle')
      return
    }
    
    // Add to bundle products array
    this.bundleProductsValue = [...this.bundleProductsValue, product]
    
    // Update UI
    this.renderSelectedProducts()
    this.updateProductCount()
    this.updateEmptyState()
    
    console.log('✅ Product added to bundle:', product.name)
  }

  // Remove product from bundle
  removeProduct(event) {
    const productId = event.currentTarget.dataset.productId
    console.log(`🗑️ Removing product: ${productId}`)
    
    // Remove from bundle products array
    this.bundleProductsValue = this.bundleProductsValue.filter(p => p.id !== productId)
    
    // Update UI
    this.renderSelectedProducts()
    this.updateProductCount()
    this.updateEmptyState()
  }

  // Render selected products in the bundle
  renderSelectedProducts() {
    if (!this.hasSelectedProductsTarget) return
    
    this.selectedProductsTarget.innerHTML = this.bundleProductsValue.map(product => `
      <div class="selected-product-item border rounded-lg p-3 mb-2">
        <div class="d-flex justify-content-between align-items-center">
          <div class="d-flex align-items-center">
            <img src="${product.image}" alt="${product.name}" class="me-2" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">
            <div>
              <h6 class="mb-0 text-sm">${product.name}</h6>
              <small class="text-muted">${product.price}</small>
            </div>
          </div>
          <button type="button" class="btn btn-sm btn-outline-danger" 
                  data-action="click->bundle-builder#removeProduct"
                  data-product-id="${product.id}">
            ×
          </button>
        </div>
      </div>
    `).join('')
  }

  // Update product count display
  updateProductCount() {
    if (this.hasProductCountTarget) {
      this.productCountTarget.textContent = this.bundleProductsValue.length
    }
  }

  // Update empty state visibility
  updateEmptyState() {
    if (this.hasEmptyStateTarget) {
      this.emptyStateTarget.style.display = this.bundleProductsValue.length === 0 ? 'block' : 'none'
    }
  }

  // Mock products data (replace with real API)
  getMockProducts(categoryId) {
    const products = {
      'supplements': [
        { id: '1', name: 'Vitamin D3', price: '$24.99', image: '/images/vitamin-d3.jpg' },
        { id: '2', name: 'Omega-3', price: '$34.99', image: '/images/omega-3.jpg' },
        { id: '3', name: 'Multivitamin', price: '$29.99', image: '/images/multivitamin.jpg' },
        { id: '4', name: 'Probiotics', price: '$39.99', image: '/images/probiotics.jpg' }
      ],
      'skincare': [
        { id: '5', name: 'Face Serum', price: '$49.99', image: '/images/face-serum.jpg' },
        { id: '6', name: 'Moisturizer', price: '$34.99', image: '/images/moisturizer.jpg' },
        { id: '7', name: 'Cleanser', price: '$24.99', image: '/images/cleanser.jpg' },
        { id: '8', name: 'Sunscreen', price: '$29.99', image: '/images/sunscreen.jpg' }
      ]
    }
    
    return products[categoryId] || []
  }
}
