// Bundle Creation Wizard Controller
// Handles multi-step wizard navigation and form submissions with Turbo
export default class BundleWizardController {
  static targets = ["step", "form", "nextButton", "prevButton", "progressBar"]
  static values = { 
    currentStep: String,
    totalSteps: Number,
    wizardState: String
  }

  connect() {
    console.log("🧙‍♂️ Bundle Wizard Controller connected")
    console.log("Current step:", this.currentStepValue)
    console.log("Wizard state:", this.wizardStateValue)
    
    this.updateProgressBar()
    this.updateNavigationButtons()
  }

  // Navigate to next step
  nextStep(event) {
    event.preventDefault()
    console.log("🚀 Next step clicked")
    
    if (this.validateCurrentStep()) {
      this.submitStepForm("next")
    }
  }

  // Navigate to previous step  
  prevStep(event) {
    event.preventDefault()
    console.log("⬅️ Previous step clicked")
    
    this.submitStepForm("prev")
  }

  // Submit current step form
  submitStepForm(direction) {
    const form = this.formTarget
    if (!form) {
      console.error("❌ No form found")
      return
    }

    // Add direction to form data
    const directionInput = document.createElement("input")
    directionInput.type = "hidden"
    directionInput.name = "direction"
    directionInput.value = direction
    form.appendChild(directionInput)

    console.log(`📤 Submitting form for ${direction} step`)
    form.requestSubmit()
  }

  // Validate current step before proceeding
  validateCurrentStep() {
    const currentStep = this.currentStepValue
    console.log(`🔍 Validating step: ${currentStep}`)

    switch (currentStep) {
      case "info":
        return this.validateInfoStep()
      case "builder":
        return this.validateBuilderStep()
      case "preview":
        return true // Preview step doesn't need validation
      default:
        return true
    }
  }

  // Validate info step (name and SKU required)
  validateInfoStep() {
    const nameField = document.querySelector('input[name="wizard[name]"]')
    const skuField = document.querySelector('input[name="wizard[sku]"]')
    
    if (!nameField?.value.trim()) {
      alert("Please enter a bundle name")
      nameField?.focus()
      return false
    }
    
    if (!skuField?.value.trim()) {
      alert("Please enter a bundle SKU")
      skuField?.focus()
      return false
    }
    
    return true
  }

  // Validate builder step (at least one product required)
  validateBuilderStep() {
    const selectedProducts = document.querySelectorAll('.selected-product-item')
    
    if (selectedProducts.length === 0) {
      alert("Please add at least one product to the bundle")
      return false
    }
    
    return true
  }

  // Update progress bar based on current step
  updateProgressBar() {
    if (!this.hasProgressBarTarget) return

    const stepNumber = this.getStepNumber(this.currentStepValue)
    const progress = (stepNumber / this.totalStepsValue) * 100
    
    this.progressBarTarget.style.width = `${progress}%`
    this.progressBarTarget.setAttribute('aria-valuenow', progress)
  }

  // Update navigation button states
  updateNavigationButtons() {
    const stepNumber = this.getStepNumber(this.currentStepValue)
    
    // Update previous button
    if (this.hasPrevButtonTarget) {
      this.prevButtonTarget.disabled = stepNumber === 1
    }
    
    // Update next button text
    if (this.hasNextButtonTarget) {
      const isLastStep = stepNumber === this.totalStepsValue
      this.nextButtonTarget.textContent = isLastStep ? "Complete Bundle" : "Next Step"
    }
  }

  // Get step number from step name
  getStepNumber(stepName) {
    const stepMap = {
      "info": 1,
      "builder": 2, 
      "preview": 3
    }
    return stepMap[stepName] || 1
  }

  // Handle successful form submission
  stepSubmitted(event) {
    console.log("✅ Step submitted successfully")
    // Turbo will handle the response and update the page
  }

  // Handle form submission errors
  stepError(event) {
    console.error("❌ Step submission error:", event.detail)
    alert("There was an error processing your request. Please try again.")
  }
}
