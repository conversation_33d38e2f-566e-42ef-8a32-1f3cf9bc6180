/*
 * This is a manifest file that'll be compiled into application.css, which will include all the files
 * listed below.
 *
 * Any CSS (and SCSS, if configured) file within this directory, lib/assets/stylesheets, or any plugin's
 * vendor/assets/stylesheets directory can be referenced here using a relative path.
 *
 * You're free to add application-wide styles to this file and they'll appear at the bottom of the
 * compiled file so the styles you add here take precedence over styles defined in any other CSS
 * files in this directory. Styles in this file should be added after the last require_* statement.
 * It is generally better to create a new file per style scope.
 *
 *= require_tree .
 *= require_self
 */

/* Basic application styles */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Bundle wizard specific styles */
.wizard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.bundle-builder-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.product-card {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 12px;
  cursor: move;
  transition: all 0.2s ease;
  background: white;
}

.product-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.product-card.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.bundle-drop-zone-full {
  flex: 1;
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  padding: 8px;
  margin: 8px;
  background: #f8f9fa;
  overflow-y: auto;
  position: relative;
}

.bundle-drop-zone-full.drag-over {
  border-color: #28a745;
  background-color: #f8fff8;
}

.drop-zone-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 6px;
  height: 120px;
  text-align: center;
}

.drop-zone-icon {
  font-size: 48px;
  opacity: 0.5;
}

.bundle-product-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 8px;
  background: white;
}

.bundle-product-info {
  flex: 1;
}

.bundle-product-name {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 2px;
}

.bundle-product-price {
  font-size: 12px;
  color: #6c757d;
}
