class FluidClient
  include HTTParty
  include Fluid::Droplets
  include Fluid::Webhooks
  include Fluid::CallbackDefinitions
  include Fluid::CallbackRegistrations

  format :json

  Error                 = Class.new(StandardError)
  AuthenticationError   = Class.new(Error)
  ResourceNotFoundError = Class.new(Error)
  APIError              = Class.new(Error)

  def initialize(auth_token = nil, company_id: nil)
    # Get API configuration from settings (like catalog droplet)
    api_setting = Setting.fluid_api
    raise "Fluid API not configured" unless api_setting&.values.present?

    config = api_setting.values
    @api_key = auth_token || config["api_key"]
    @base_url = config["base_url"] || "http://localhost:3000"

    # Use provided company_id or try to get from Company model
    @company_id = company_id || get_company_id

    raise "API key not configured" unless @api_key.present?
    raise "Company ID not found" unless @company_id.present?

    Rails.logger.info("FluidClient: Initialized with base_url: #{@base_url}")
    Rails.logger.info("FluidClient: API key: #{@api_key[0..10]}...")
    Rails.logger.info("FluidClient: Company ID: #{@company_id}")

    # Set instance-specific configuration
    @headers = {
      'Content-Type' => 'application/json',
      'Authorization' => "Bearer #{@api_key}"
    }
  end

  def get(path, options = {})
    make_request(:get, path, options)
  end

  def post(path, options = {})
    make_request(:post, path, options)
  end

  def put(path, options = {})
    make_request(:put, path, options)
  end

  def delete(path, options = {})
    make_request(:delete, path, options)
  end

  # Test method to verify class loading
  def test_method
    "FluidClient is working!"
  end

  # Specific methods for categories and products
  def list_categories(params = {})
    Rails.logger.info "FluidClient: Getting categories"

    default_params = {
      per_page: 50,
      status: true  # true = active categories
    }

    response = get("/api/company/v1/categories", {
      query: default_params.merge(params)
    })

    # Extract categories from response
    if response.is_a?(Hash)
      response["categories"] || response
    else
      response
    end
  end

  def list_products(params = {})
    Rails.logger.info "FluidClient: Getting products"

    default_params = {
      per_page: 50
    }

    response = get("/api/company/v1/products", {
      query: default_params.merge(params)
    })

    # Extract products from response
    if response.is_a?(Hash)
      response["products"] || response
    else
      response
    end
  end

  def get_products_by_category(category_id, params = {})
    Rails.logger.info "FluidClient: Getting products for category #{category_id}"

    default_params = {
      per_page: 50,
      category_id: category_id
    }

    response = get("/api/company/v1/products", {
      query: default_params.merge(params)
    })

    # Extract products from response
    if response.is_a?(Hash)
      response["products"] || response
    else
      response
    end
  end

private

  def make_request(method, path, options = {})
    url = "#{@base_url}#{path}"

    # Prepare request options
    request_options = {
      headers: @headers,
      timeout: 30
    }

    # Add query parameters for GET requests
    if options[:query] && method == :get
      request_options[:query] = options[:query]
    end

    # Add body for POST/PUT requests
    if options[:body] && [:post, :put].include?(method)
      request_options[:body] = options[:body].is_a?(Hash) ? options[:body].to_json : options[:body]
    end

    Rails.logger.info("FluidClient: #{method.upcase} #{url}")
    Rails.logger.debug("FluidClient: Request options: #{request_options.except(:headers)}")

    # Make the request using HTTParty
    response = HTTParty.send(method, url, request_options)

    Rails.logger.info("FluidClient: Response code: #{response.code}")
    Rails.logger.debug("FluidClient: Response body: #{response.body[0..500]}...")

    handle_response(response)
  rescue => e
    Rails.logger.error("FluidClient: Request failed: #{e.message}")
    raise APIError, "Request failed: #{e.message}"
  end

  def get_company_id
    # Try to get company_id from the current company context
    company = Thread.current[:current_company] || Company.first
    return company&.fluid_company_id || company&.id if company

    # Fallback to a default company ID for testing
    return *********
  end

  def handle_response(response)
    case response.code
    when 200..299
      response.parsed_response
    when 401
      Rails.logger.error("FluidClient: Authentication failed - Invalid API key")
      raise AuthenticationError, "Authentication failed: #{response.body}"
    when 404
      Rails.logger.error("FluidClient: Resource not found")
      raise ResourceNotFoundError, "Resource not found: #{response.body}"
    when 422
      Rails.logger.error("FluidClient: Validation error: #{response.body}")
      raise APIError, "Validation error: #{response.body}"
    else
      Rails.logger.error("FluidClient: API error #{response.code}: #{response.body}")
      raise APIError, "API error #{response.code}: #{response.body}"
    end
  end
end
