<header class="p-2 flex justify-end">
  <%= link_to "Sign in", new_user_session_path %>
</header>

<div class="container mx-auto p-8">
  <div class="max-w-2xl mx-auto">
    <h1 class="text-3xl font-bold text-gray-900 mb-8">Fluid Catalog Droplet</h1>

    <div class="bg-white shadow-lg rounded-lg p-6">
      <h2 class="text-xl font-semibold text-gray-800 mb-4">Fluid Catalog Browser</h2>

      <% if Setting.fluid_api&.values&.dig("api_key").present? %>
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <h3 class="text-green-800 font-medium mb-2">✅ API Configured</h3>
          <p class="text-green-700 text-sm">Your Fluid API credentials are configured. Click below to browse your catalog.</p>
        </div>

        <button id="test-api-btn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
          Browse Catalog
        </button>
      <% else %>
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <h3 class="text-yellow-800 font-medium mb-2">⚠️ Configuration Required</h3>
          <p class="text-yellow-700 text-sm mb-3">Please configure your Fluid API credentials to browse your catalog.</p>
          <p class="text-yellow-700 text-sm">
            Go to: <strong>Admin Panel → Settings → Fluid API</strong>
          </p>
        </div>

        <button id="test-api-btn" class="bg-gray-400 text-white font-bold py-2 px-4 rounded cursor-not-allowed" disabled>
          Configure API First
        </button>
      <% end %>

      <div id="loading" class="hidden mt-4">
        <div class="flex items-center">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
          <span class="ml-2 text-gray-600">Testing API connection...</span>
        </div>
      </div>

      <div id="results" class="mt-6 hidden">
        <div id="success-message" class="hidden p-4 mb-4 text-green-700 bg-green-100 rounded-lg">
          <h3 class="font-bold">Success!</h3>
          <p id="success-text"></p>
          <div id="company-info" class="mt-2 text-sm text-green-600"></div>
        </div>

        <div id="error-message" class="hidden p-4 mb-4 text-red-700 bg-red-100 rounded-lg">
          <h3 class="font-bold">Error!</h3>
          <p id="error-text"></p>
        </div>

        <div id="data-display" class="hidden">
          <!-- Tabs Navigation -->
          <div class="border-b border-gray-200 mb-4">
            <nav class="-mb-px flex space-x-8">
              <button id="products-tab" class="tab-button active py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600">
                Products
              </button>
              <button id="categories-tab" class="tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                Categories
              </button>
              <button id="bundle-builder-tab" class="tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                🎁 Bundle Builder
              </button>
            </nav>
          </div>

          <!-- Products Tab Content -->
          <div id="products-content" class="tab-content">
            <div class="bg-blue-50 rounded-lg p-4 mb-4">
              <h3 class="text-lg font-medium text-gray-800 mb-2">Products</h3>
              <div id="products-data" class="text-sm text-gray-700">
                <p class="text-gray-500">No products data yet.</p>
              </div>
            </div>
          </div>

          <!-- Categories Tab Content -->
          <div id="categories-content" class="tab-content hidden">
            <div class="bg-green-50 rounded-lg p-4 mb-4">
              <h3 class="text-lg font-medium text-gray-800 mb-2">Categories</h3>
              <div id="categories-data" class="text-sm text-gray-700">
                <p class="text-gray-500">No categories data yet.</p>
              </div>
            </div>
          </div>

          <!-- Bundle Builder Tab Content -->
          <div id="bundle-builder-content" class="tab-content hidden">
            <!-- Notification Area -->
            <div id="notification-area" class="mb-4"></div>

            <div class="h-96 grid grid-cols-2 gap-4">
              <!-- Left Panel -->
              <div class="grid grid-rows-2 gap-4">
                <!-- Categories Panel -->
                <div class="bg-purple-50 rounded-lg p-4 border-2 border-purple-200">
                  <h3 class="text-lg font-medium text-purple-800 mb-3">📂 Categories</h3>
                  <div id="categories-grid" class="grid grid-cols-4 gap-2">
                    <div class="category-card bg-purple-100 hover:bg-purple-200 p-3 rounded-lg cursor-pointer text-center transition-colors">
                      <div class="text-2xl mb-1">🍔</div>
                      <div class="text-xs font-medium">Food</div>
                    </div>
                    <div class="category-card bg-purple-100 hover:bg-purple-200 p-3 rounded-lg cursor-pointer text-center transition-colors">
                      <div class="text-2xl mb-1">👕</div>
                      <div class="text-xs font-medium">Clothing</div>
                    </div>
                    <div class="category-card bg-purple-100 hover:bg-purple-200 p-3 rounded-lg cursor-pointer text-center transition-colors">
                      <div class="text-2xl mb-1">📱</div>
                      <div class="text-xs font-medium">Tech</div>
                    </div>
                    <div class="category-card bg-purple-100 hover:bg-purple-200 p-3 rounded-lg cursor-pointer text-center transition-colors">
                      <div class="text-2xl mb-1">🏠</div>
                      <div class="text-xs font-medium">Home</div>
                    </div>
                  </div>
                </div>

                <!-- Products Panel -->
                <div class="bg-blue-50 rounded-lg p-4 border-2 border-blue-200">
                  <h3 class="text-lg font-medium text-blue-800 mb-3">📦 Products</h3>
                  <div id="products-grid" class="grid grid-cols-3 gap-2 max-h-40 overflow-y-auto">
                    <div class="product-card bg-blue-100 hover:bg-blue-200 p-2 rounded-lg cursor-move text-center transition-colors" draggable="true">
                      <div class="text-lg mb-1">🍕</div>
                      <div class="text-xs font-medium">Pizza</div>
                      <div class="text-xs text-gray-600">$12.99</div>
                    </div>
                    <div class="product-card bg-blue-100 hover:bg-blue-200 p-2 rounded-lg cursor-move text-center transition-colors" draggable="true">
                      <div class="text-lg mb-1">🍔</div>
                      <div class="text-xs font-medium">Burger</div>
                      <div class="text-xs text-gray-600">$8.99</div>
                    </div>
                    <div class="product-card bg-blue-100 hover:bg-blue-200 p-2 rounded-lg cursor-move text-center transition-colors" draggable="true">
                      <div class="text-lg mb-1">🍟</div>
                      <div class="text-xs font-medium">Fries</div>
                      <div class="text-xs text-gray-600">$4.99</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Right Panel - Bundle Area -->
              <div class="bg-green-50 rounded-lg p-4 border-2 border-green-200 border-dashed">
                <h3 class="text-lg font-medium text-green-800 mb-3">🎁 Bundle Builder</h3>
                <div id="bundle-drop-zone" class="min-h-64 bg-white rounded-lg border-2 border-dashed border-green-300 p-4 text-center">
                  <div class="text-green-600 mb-4">
                    <div class="text-4xl mb-2">🎁</div>
                    <div class="font-medium">Drag products here to create your bundle</div>
                    <div class="text-sm text-gray-500 mt-1">Mix and match from different categories</div>
                  </div>

                  <div id="bundle-products" class="grid grid-cols-2 gap-2 mt-4">
                    <!-- Dropped products will appear here -->
                  </div>

                  <div id="bundle-summary" class="mt-4 p-3 bg-green-100 rounded-lg hidden">
                    <div class="font-medium text-green-800">Bundle Summary</div>
                    <div id="bundle-count" class="text-sm text-green-600">0 products</div>
                    <div id="bundle-total" class="text-sm text-green-600">Total: $0.00</div>
                    <button id="save-bundle" class="mt-2 bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm">
                      Save Bundle
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Raw API Response (for debugging) -->
          <details class="mt-4">
            <summary class="cursor-pointer text-sm text-gray-600 hover:text-gray-800">Show Raw API Response</summary>
            <div class="bg-gray-100 rounded-lg p-4 mt-2">
              <pre id="data-content" class="text-sm text-gray-700 whitespace-pre-wrap overflow-auto max-h-96"></pre>
            </div>
          </details>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const testBtn = document.getElementById('test-api-btn');
  const loading = document.getElementById('loading');
  const results = document.getElementById('results');
  const successMessage = document.getElementById('success-message');
  const errorMessage = document.getElementById('error-message');
  const dataDisplay = document.getElementById('data-display');
  const successText = document.getElementById('success-text');
  const errorText = document.getElementById('error-text');
  const dataContent = document.getElementById('data-content');
  const companyInfo = document.getElementById('company-info');

  // Tab elements
  const productsTab = document.getElementById('products-tab');
  const categoriesTab = document.getElementById('categories-tab');
  const bundleBuilderTab = document.getElementById('bundle-builder-tab');
  const productsContent = document.getElementById('products-content');
  const categoriesContent = document.getElementById('categories-content');
  const bundleBuilderContent = document.getElementById('bundle-builder-content');
  const productsData = document.getElementById('products-data');
  const categoriesData = document.getElementById('categories-data');

  // Tab switching functionality
  function switchTab(activeTab, activeContent, inactiveTabs, inactiveContents) {
    // Handle single tab or array of tabs
    const tabsToDeactivate = Array.isArray(inactiveTabs) ? inactiveTabs : [inactiveTabs];
    const contentsToHide = Array.isArray(inactiveContents) ? inactiveContents : [inactiveContents];

    // Update active tab styles
    activeTab.classList.add('border-blue-500', 'text-blue-600');
    activeTab.classList.remove('border-transparent', 'text-gray-500');

    // Update inactive tabs styles
    tabsToDeactivate.forEach(tab => {
      if (tab) {
        tab.classList.add('border-transparent', 'text-gray-500');
        tab.classList.remove('border-blue-500', 'text-blue-600');
      }
    });

    // Show active content
    activeContent.classList.remove('hidden');

    // Hide inactive contents
    contentsToHide.forEach(content => {
      if (content) {
        content.classList.add('hidden');
      }
    });
  }

  if (productsTab) {
    productsTab.addEventListener('click', function() {
      switchTab(productsTab, productsContent, [categoriesTab, bundleBuilderTab], [categoriesContent, bundleBuilderContent]);
    });
  }

  if (categoriesTab) {
    categoriesTab.addEventListener('click', function() {
      switchTab(categoriesTab, categoriesContent, [productsTab, bundleBuilderTab], [productsContent, bundleBuilderContent]);
    });
  }

  if (bundleBuilderTab) {
    bundleBuilderTab.addEventListener('click', function() {
      console.log('Bundle Builder tab clicked!');
      switchTab(bundleBuilderTab, bundleBuilderContent, [productsTab, categoriesTab], [productsContent, categoriesContent]);
    });
  }

  function formatData(data, type) {
    if (!data || (Array.isArray(data) && data.length === 0)) {
      return `<p class="text-gray-500">No ${type} found.</p>`;
    }

    if (Array.isArray(data)) {
      return data.map(item => {
        if (type === 'products') {
          return `
            <div class="border-b border-gray-200 pb-2 mb-2">
              <div class="font-medium">${item.title || item.name || 'Unnamed Product'}</div>
              <div class="text-sm text-gray-600">ID: ${item.id || 'N/A'}</div>
              ${item.price_in_currency ? `<div class="text-sm text-gray-600">Price: ${item.price_in_currency}</div>` : ''}
              ${item.sku ? `<div class="text-sm text-gray-600">SKU: ${item.sku}</div>` : ''}
            </div>
          `;
        } else {
          return `
            <div class="border-b border-gray-200 pb-2 mb-2">
              <div class="font-medium">${item.title || item.name || 'Unnamed Category'}</div>
              <div class="text-sm text-gray-600">ID: ${item.id || 'N/A'}</div>
              <div class="text-sm text-gray-600">Status: ${item.active ? 'Active' : 'Draft'}</div>
              ${item.products && item.products.length > 0 ? `<div class="text-sm text-gray-600">Products: ${item.products.length}</div>` : ''}
            </div>
          `;
        }
      }).join('');
    }

    return `<pre class="text-sm">${JSON.stringify(data, null, 2)}</pre>`;
  }

  if (testBtn) {
    testBtn.addEventListener('click', function() {
      if (testBtn.disabled) return;

      // Reset UI
      loading.classList.remove('hidden');
      results.classList.add('hidden');
      successMessage.classList.add('hidden');
      errorMessage.classList.add('hidden');
      dataDisplay.classList.add('hidden');
      testBtn.disabled = true;

      // Make API call
      fetch('/test_api')
        .then(response => response.json())
        .then(data => {
          loading.classList.add('hidden');
          results.classList.remove('hidden');
          testBtn.disabled = false;

          if (data.success) {
            successMessage.classList.remove('hidden');
            successText.textContent = data.message;

            // Show company ID if available
            if (data.company_id) {
              companyInfo.textContent = `Connected to Company ID: ${data.company_id}`;
            }

            if (data.products || data.categories) {
              dataDisplay.classList.remove('hidden');

              // Update products tab
              if (productsData) {
                productsData.innerHTML = formatData(data.products, 'products');
              }

              // Update categories tab
              if (categoriesData) {
                categoriesData.innerHTML = formatData(data.categories, 'categories');
              }

              // Update raw data
              if (dataContent) {
                dataContent.textContent = JSON.stringify(data, null, 2);
              }
            }
          } else {
            errorMessage.classList.remove('hidden');
            errorText.textContent = data.message + (data.error ? ': ' + data.error : '');
          }
        })
        .catch(error => {
          loading.classList.add('hidden');
          results.classList.remove('hidden');
          errorMessage.classList.remove('hidden');
          errorText.textContent = 'Network error: ' + error.message;
          testBtn.disabled = false;
        });
    });
  }

  // Bundle Builder Functionality
  let bundleProducts = [];

  // Notification system
  function showNotification(message, type = 'info') {
    const notificationArea = document.getElementById('notification-area');
    const notification = document.createElement('div');

    const bgColor = {
      'success': 'bg-green-100 border-green-400 text-green-700',
      'error': 'bg-red-100 border-red-400 text-red-700',
      'warning': 'bg-yellow-100 border-yellow-400 text-yellow-700',
      'info': 'bg-blue-100 border-blue-400 text-blue-700'
    }[type];

    notification.className = `${bgColor} border px-4 py-3 rounded mb-2 flex justify-between items-center animate-pulse`;
    notification.innerHTML = `
      <span>${message}</span>
      <button class="ml-4 text-lg font-bold opacity-70 hover:opacity-100" onclick="this.parentElement.remove()">×</button>
    `;

    notificationArea.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 3000);
  }

  // Category selection
  document.querySelectorAll('.category-card').forEach(card => {
    card.addEventListener('click', function() {
      // Remove active state from all categories
      document.querySelectorAll('.category-card').forEach(c => {
        c.classList.remove('bg-purple-300', 'ring-2', 'ring-purple-500');
        c.classList.add('bg-purple-100');
      });

      // Add active state to clicked category
      this.classList.remove('bg-purple-100');
      this.classList.add('bg-purple-300', 'ring-2', 'ring-purple-500');

      // Load products for this category (mock data for now)
      loadProductsForCategory(this.querySelector('.text-xs').textContent);
    });
  });

  function loadProductsForCategory(categoryName) {
    const productsGrid = document.getElementById('products-grid');

    // Mock products based on category
    const mockProducts = {
      'Food': [
        { name: 'Pizza', price: 12.99, emoji: '🍕' },
        { name: 'Burger', price: 8.99, emoji: '🍔' },
        { name: 'Fries', price: 4.99, emoji: '🍟' },
        { name: 'Taco', price: 6.99, emoji: '🌮' },
        { name: 'Soda', price: 2.99, emoji: '🥤' },
        { name: 'Ice Cream', price: 5.99, emoji: '🍦' }
      ],
      'Clothing': [
        { name: 'T-Shirt', price: 19.99, emoji: '👕' },
        { name: 'Jeans', price: 49.99, emoji: '👖' },
        { name: 'Sneakers', price: 79.99, emoji: '👟' },
        { name: 'Hat', price: 24.99, emoji: '🧢' }
      ],
      'Tech': [
        { name: 'Phone', price: 699.99, emoji: '📱' },
        { name: 'Laptop', price: 999.99, emoji: '💻' },
        { name: 'Headphones', price: 199.99, emoji: '🎧' },
        { name: 'Watch', price: 299.99, emoji: '⌚' }
      ],
      'Home': [
        { name: 'Lamp', price: 39.99, emoji: '💡' },
        { name: 'Pillow', price: 19.99, emoji: '🛏️' },
        { name: 'Plant', price: 29.99, emoji: '🪴' },
        { name: 'Candle', price: 14.99, emoji: '🕯️' }
      ]
    };

    const products = mockProducts[categoryName] || [];

    productsGrid.innerHTML = products.map(product => `
      <div class="product-card bg-blue-100 hover:bg-blue-200 p-2 rounded-lg cursor-move text-center transition-colors"
           draggable="true"
           data-product='${JSON.stringify(product)}'>
        <div class="text-lg mb-1">${product.emoji}</div>
        <div class="text-xs font-medium">${product.name}</div>
        <div class="text-xs text-gray-600">$${product.price}</div>
      </div>
    `).join('');

    // Add drag event listeners to new products
    setupDragAndDrop();
  }

  function setupDragAndDrop() {
    // Drag start
    document.querySelectorAll('.product-card[draggable="true"]').forEach(card => {
      card.addEventListener('dragstart', function(e) {
        e.dataTransfer.setData('text/plain', this.dataset.product);
        this.classList.add('opacity-50');
      });

      card.addEventListener('dragend', function(e) {
        this.classList.remove('opacity-50');
      });
    });

    // Drop zone events
    const dropZone = document.getElementById('bundle-drop-zone');

    dropZone.addEventListener('dragover', function(e) {
      e.preventDefault();
      this.classList.add('bg-green-100', 'border-green-400');
    });

    dropZone.addEventListener('dragleave', function(e) {
      this.classList.remove('bg-green-100', 'border-green-400');
    });

    dropZone.addEventListener('drop', function(e) {
      e.preventDefault();
      this.classList.remove('bg-green-100', 'border-green-400');

      const productData = JSON.parse(e.dataTransfer.getData('text/plain'));
      addProductToBundle(productData);
    });
  }

  function addProductToBundle(product) {
    // Check if product already in bundle
    if (bundleProducts.find(p => p.name === product.name)) {
      showNotification(`${product.name} is already in your bundle!`, 'warning');
      return;
    }

    bundleProducts.push(product);
    showNotification(`${product.name} added to bundle! 🎉`, 'success');
    updateBundleDisplay();
  }

  function updateBundleDisplay() {
    const bundleProductsContainer = document.getElementById('bundle-products');
    const bundleSummary = document.getElementById('bundle-summary');
    const bundleCount = document.getElementById('bundle-count');
    const bundleTotal = document.getElementById('bundle-total');

    // Update products display
    bundleProductsContainer.innerHTML = bundleProducts.map((product, index) => `
      <div class="bg-white border border-green-300 rounded-lg p-2 text-center relative">
        <button class="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-4 h-4 text-xs leading-none"
                onclick="removeFromBundle(${index})">×</button>
        <div class="text-lg mb-1">${product.emoji}</div>
        <div class="text-xs font-medium">${product.name}</div>
        <div class="text-xs text-gray-600">$${product.price}</div>
      </div>
    `).join('');

    // Update summary
    const total = bundleProducts.reduce((sum, product) => sum + product.price, 0);
    bundleCount.textContent = `${bundleProducts.length} products`;
    bundleTotal.textContent = `Total: $${total.toFixed(2)}`;

    // Show/hide summary
    if (bundleProducts.length > 0) {
      bundleSummary.classList.remove('hidden');
    } else {
      bundleSummary.classList.add('hidden');
    }
  }

  // Make removeFromBundle global
  window.removeFromBundle = function(index) {
    bundleProducts.splice(index, 1);
    updateBundleDisplay();
  };

  // Save bundle functionality
  document.getElementById('save-bundle').addEventListener('click', function() {
    if (bundleProducts.length === 0) {
      showNotification('Add some products to your bundle first! 📦', 'warning');
      return;
    }

    // Show loading state
    const saveButton = this;
    const originalText = saveButton.textContent;
    saveButton.textContent = 'Creating Bundle...';
    saveButton.disabled = true;

    // Prepare bundle data with auto-generated name
    const timestamp = new Date().toLocaleString();
    const productNames = bundleProducts.map(p => p.name).slice(0, 3).join(', ');
    const bundleName = `Bundle: ${productNames}${bundleProducts.length > 3 ? ` +${bundleProducts.length - 3} more` : ''}`;

    const bundleData = {
      bundle: {
        name: bundleName,
        description: `Custom bundle containing ${bundleProducts.length} products created on ${timestamp}`,
        products: bundleProducts
      }
    };

    // Send to server
    fetch('/create_bundle_product', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
      },
      body: JSON.stringify(bundleData)
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert(`🎉 Bundle created successfully!\n\nProduct ID: ${data.product?.product?.id || 'N/A'}\nTotal: $${data.bundle_data?.total_price || 0}\nProducts: ${data.bundle_data?.product_count || 0}`);

        // Reset bundle
        bundleProducts = [];
        updateBundleDisplay();

        console.log('Bundle created:', data);
      } else {
        alert(`❌ Error creating bundle:\n${data.message}`);
        console.error('Bundle creation failed:', data);
      }
    })
    .catch(error => {
      alert(`❌ Network error:\n${error.message}`);
      console.error('Network error:', error);
    })
    .finally(() => {
      // Restore button state
      saveButton.textContent = originalText;
      saveButton.disabled = false;
    });
  });

  // Initialize drag and drop
  setupDragAndDrop();
});
</script>
